"use client";

import { AdminClient, APIResponseType } from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Button } from "@geon-ui/react/primitives/button";
import { Checkbox } from "@geon-ui/react/primitives/checkbox";
import { Label } from "@geon-ui/react/primitives/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@geon-ui/react/primitives/popover";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableRow,
} from "@geon-ui/react/primitives/table";
import React, { useMemo, useState } from "react";

export type PermRow = { authorGroupId: string; authorGroupNm: string };

type AdminPopoverProps = {
  trigger?: React.ReactNode;
  defaultSelected?: string[];
  onAdd?: (selected: string[]) => void;
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";
  sideOffset?: number;
  alignOffset?: number;
  title?: string;
};

export function AdminPopover({
  trigger,
  defaultSelected = [],
  onAdd,
  side = "right",
  align = "end",
  sideOffset = 12,
  alignOffset = 100,
  title,
  client,
}: AdminPopoverProps & {
  client: AdminClient;
}) {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<Set<string>>(
    new Set(defaultSelected),
  );

  const toggle = (name: string, checked: boolean | "indeterminate") => {
    if (checked === "indeterminate") return;
    const next = new Set(selected);
    if (checked === true) next.add(name);
    else next.delete(name);
    setSelected(next);
  };
  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<AdminClient["author"]["menu"]>
  >({
    queryKey: ["author/business", { authorType: "business" }],
    queryFn: () =>
      client.author.menu({
        authorType: "business",
      }),
  });

  const items: PermRow[] = useMemo(() => {
    if (!data) return [];
    const r: any = (data as any).result;
    if (Array.isArray(r)) return r as PermRow[];
    if (r && Array.isArray(r.resultList)) return r.resultList as PermRow[];
    return [];
  }, [data]);

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading parcel data: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  const handleAdd = () => {
    onAdd?.(Array.from(selected));
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal={false}>
      <PopoverTrigger asChild>{trigger}</PopoverTrigger>
      <PopoverContent
        side={side}
        align={align}
        sideOffset={sideOffset}
        alignOffset={alignOffset}
      >
        <div className="mb-4 text-center text-base font-semibold">{title}</div>
        <Table className="w-full border-collapse border">
          <TableBody>
            {items.map((row) => {
              const name = row.authorGroupNm;
              return (
                <TableRow key={row.authorGroupId}>
                  <TableCell>
                    <Label
                      htmlFor={row.authorGroupNm}
                      className="py-2 text-left"
                    >
                      {row.authorGroupNm}
                    </Label>
                  </TableCell>
                  <TableCell>
                    <Checkbox
                      id={name}
                      checked={selected.has(name)}
                      onCheckedChange={(v) => toggle(name, v)}
                      className="m-2"
                    />
                  </TableCell>
                </TableRow>
              );
            })}
            {items.length === 0 && (
              <TableRow>
                <TableCell className="py-6 text-center" colSpan={2}>
                  선택 가능한 권한이 없습니다.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <div className="mt-3 flex justify-end gap-2">
          <Button type="button" variant="ghost" onClick={() => setOpen(false)}>
            취소
          </Button>
          <Button type="button" variant="secondary" onClick={handleAdd}>
            추가
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
