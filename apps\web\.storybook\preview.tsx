import "@config/tailwind/globals.css";

import { Toaster } from "@geon-ui/react/primitives/sonner";
import { TooltipProvider } from "@geon-ui/react/primitives/tooltip";
import type { Preview } from "@storybook/nextjs";
import { NextIntlClientProvider } from "next-intl";

// @ts-expect-error allow arbitrary extension option disabled
import messages from "../messages/ko.json" with { type: "json" };

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    docs: {
      codePanel: true,
    },
  },
  decorators: [
    (Story) => (
      <NextIntlClientProvider locale="ko" messages={messages}>
        <TooltipProvider>
          <Story />
          <Toaster />
        </TooltipProvider>
      </NextIntlClientProvider>
    ),
  ],
};

export default preview;
