"use client";
import { CaptureResult, MapCapture, MapCaptureConfig } from "@geon-map/core";
import { useCallback, useEffect, useRef, useState } from "react";

import { useMap } from "./use-map";
import { useMapActions } from "./use-map-actions";

export interface UseMapCaptureOptions extends MapCaptureConfig {
  onCapture?: (result: CaptureResult) => void;
}

export function useMapCapture(options?: UseMapCaptureOptions) {
  const { map, isLoading, error } = useMap();
  const { getTarget } = useMapActions();
  const [isCapturing, setIsCapturing] = useState(false);
  const [captureResult, setCaptureResult] = useState<CaptureResult | null>(
    null,
  );
  const [isReady, setIsReady] = useState(false);
  const captureRef = useRef<MapCapture | null>(null);
  // options를 ref로 저장하여 안정적인 참조 유지
  const optionsRef = useRef(options);
  optionsRef.current = options;

  // MapCapture 인스턴스 생성/제거
  useEffect(() => {
    const mapTarget = getTarget(); // [최희라] useMap 훅에서 타겟 자체를 가져오도록 수정할 예정
    if (!mapTarget || typeof window === "undefined" || isLoading) {
      setIsReady(false);
      return;
    }

    const initializeCapture = () => {
      try {
        // 현재 options를 ref에서 가져옴
        const currentOptions = optionsRef.current;
        const { onCapture, ...config } = currentOptions || {};
        captureRef.current = new MapCapture(mapTarget, config);

        // 초기화 완료 콜백 설정
        captureRef.current.onInitialized(() => {
          if (captureRef.current) {
            // 캡처 결과 콜백 설정
            captureRef.current.onCapture((result) => {
              setCaptureResult(result);
              setIsCapturing(false); // 캡처 완료 시 자동으로 비활성화
              optionsRef.current?.onCapture?.(result);
            });
            setIsReady(true);
          }
        });
      } catch (error) {
        console.error("MapCapture 초기화 실패:", error);
        setIsReady(false);
      }
    };

    initializeCapture();

    return () => {
      captureRef.current?.destroy();
      captureRef.current = null;
      setIsReady(false);
    };
  }, [map, isLoading]);

  // 영역선택 시작
  const startCapturing = useCallback(() => {
    if (!captureRef.current || !isReady) return;
    captureRef.current.startSelection();
    setIsCapturing(true);
    setCaptureResult(null); // 이전 결과 초기화
  }, [isReady]);

  // 영역선택 종료
  const stopCapturing = useCallback(() => {
    if (!captureRef.current || !isReady) return;
    captureRef.current.stopSelection();
    setIsCapturing(false);
  }, [isReady]);

  // 현재 화면 전체 캡처
  const captureCurrentMap = useCallback(
    async (options?: {
      quality?: number;
      pixelRatio?: number;
      backgroundColor?: string;
    }) => {
      if (!captureRef.current || !isReady) return null;

      try {
        const result = captureRef.current.captureCurrentMap();
        if (result) {
          setCaptureResult(result);
          optionsRef.current?.onCapture?.(result);
        }
        return result;
      } catch (error) {
        console.error("현재 화면 캡처 실패:", error);
        return null;
      }
    },
    [isReady],
  );

  // 캡처 영역 지우기
  const clearCapture = useCallback(() => {
    if (!captureRef.current || !isReady) return;
    captureRef.current.clearSelection();
  }, [isReady]);

  // 리사이즈 처리
  const resize = useCallback(() => {
    if (!captureRef.current || !isReady) return;
    captureRef.current.resize();
  }, [isReady]);

  // 리사이즈 처리
  const resetCapture = useCallback(() => {
    if (!captureRef.current || !isReady) return;
    // 1. 선택 영역 모드 종료
    captureRef.current.stopSelection();
    // 2. 선택 영역 지우기
    captureRef.current.clearSelection();
    // 3. 상태 초기화
    setIsCapturing(false);
    setCaptureResult(null);
  }, [isReady]);

  return {
    startCapturing,
    stopCapturing,
    clearCapture,
    captureCurrentMap,
    resetCapture,
    resize,
    isCapturing,
    captureResult,
    isReady, // 준비 상태 추가
  };
}
