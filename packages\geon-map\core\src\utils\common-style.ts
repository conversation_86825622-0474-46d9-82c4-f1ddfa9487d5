// ====== 공통 타입 (네가 확정한 버전) ======
export type Rgba = [number, number, number, number];

export interface Stroke {
  color?: Rgba;
  width?: number;
  lineDash?: number[];
  lineCap?: "butt" | "round" | "square";
  lineJoin?: "miter" | "round" | "bevel";
}
export interface Fill {
  color?: Rgba;
}
export interface Mark {
  shape?: "Circle" | "Square" | "Star" | "Triangle" | string;
  radius?: number;
  src?: string;
  rotation?: number;
  scale?: number;
  anchor?: [number, number];
}
export interface TextStyle {
  label?: string;
  fill?: Rgba;
  stroke?: { color?: Rgba; width?: number };
  fontFamily?: string;
  fontStyle?: "normal" | "italic";
  fontWeight?: "normal" | "bold" | number;
  fontSize?: number;
  placement?: "point" | "line";
  overflow?: boolean;
  offsetX?: number;
  offsetY?: number;
  haloColor?: Rgba;
  haloWidth?: number;
}
export type Symbolizer =
  | {
      kind: "polygon";
      fill?: Fill;
      stroke?: Stroke;
      zIndex?: number;
    }
  | { kind: "line"; stroke?: Stroke; zIndex?: number }
  | {
      kind: "point";
      mark?: Mark;
      fill?: Fill;
      stroke?: Stroke;
      zIndex?: number;
    }
  | { kind: "text"; text: TextStyle; zIndex?: number };

export type Filter =
  | { type: "else" }
  | { type: "ogc"; value: any }
  | { type: "cql"; value: string }
  | { type: "attr"; where: Record<string, string | number | boolean> };

export interface When {
  filter?: Filter;
  scale?: { min?: number; max?: number };
  resolution?: { min?: number; max?: number };
  geometry?: "point" | "line" | "polygon" | "text";
}

export interface CommonStyleItem {
  when?: When;
  style: { symbolizers: Symbolizer[]; name?: string; legendLabel?: string };
  order?: number;
}

export interface CommonStyleSpec {
  name?: string;
  defaults?: { symbolizers?: Symbolizer[] };
  items: CommonStyleItem[];
}

// ====== 스태틱 변환 유틸 ======
export class CommonStyle {
  /**
   * odfStyleObject : ODF 레이어 스타일 객체 (wmsLayer.getSLD().getObject() 또는 wfsLayer.getStyle().getObject())
   * **/
  static toCommonSpec(
    odfStyleObject: any,
    service?: "WMS" | "WFS",
  ): CommonStyleSpec {
    // hint가 있으면 우선
    if (service === "WMS") return this.fromWms(odfStyleObject);
    if (service === "WFS") return this.fromWfs(odfStyleObject);

    // 휴리스틱
    if (odfStyleObject && Array.isArray(odfStyleObject.rules))
      return this.fromWms(odfStyleObject);
    if (
      Array.isArray(odfStyleObject) &&
      odfStyleObject.length &&
      odfStyleObject[0].style
    )
      return this.fromWfs(odfStyleObject);

    // 모르면 비어있는 스펙
    return { name: "Unknown", items: [] };
  }

  static async fromOdfLayer(odfLayer: any): Promise<CommonStyleSpec> {
    if (!odfLayer) {
      return { name: "Layer Not Found", items: [] };
    }

    try {
      // LayerCore import 필요
      const styleObject = odfLayer.getStyle()?.getObject();
      if (!styleObject) {
        return { name: "Style Not Found", items: [] };
      }
      return this.toCommonSpec(styleObject);
    } catch (error) {
      console.error("Failed to get style from ODF layer:", error);
      return { name: "Style Error", items: [] };
    }
  }
  /* ===================== WMS ===================== */

  /** ODF WMS 스타일 객체 (getSLD().getObject()) → CommonStyleSpec */
  static fromWms(odfWmsStyleObject: any): CommonStyleSpec {
    const name: string | undefined = odfWmsStyleObject?.name;
    const rules: any[] = Array.isArray(odfWmsStyleObject?.rules)
      ? odfWmsStyleObject.rules
      : [];

    const items: CommonStyleItem[] = rules.map((rule, idx) => {
      const when: When = {};
      // scaleDenominator
      if (rule.scaleDenominator) {
        when.scale = {
          min: rule.scaleDenominator.min,
          max: rule.scaleDenominator.max,
        };
      }
      // filter → CQL로 단순화 (배열형식 파싱)
      if (rule.filter) {
        const f = this._wmsArrayFilterToCql(rule.filter);
        when.filter = f ?? undefined;
      }

      // symbolizers
      const symbolizers: Symbolizer[] = [];
      const syms: any[] = Array.isArray(rule.symbolizers)
        ? rule.symbolizers
        : [];

      for (const s of syms) {
        if (typeof s?.kind !== "string") continue;

        if (s.kind.toLowerCase() === "text") {
          // 텍스트
          const fill = this.hexOrCssToRgba(s.color, s.fillOpacity ?? 1);
          const halo = this.hexOrCssToRgba(s.haloColor, s.strokeOpacity ?? 1); // strokeOpacity가 halo로 들어오는 케이스
          symbolizers.push({
            kind: "text",
            text: {
              label: s.label,
              fill,
              haloColor: halo,
              haloWidth: s.haloWidth,
              fontFamily: Array.isArray(s.font) ? s.font[0] : s.font,
              fontStyle: s.fontStyle,
              fontWeight: s.fontWeight,
              fontSize: s.size,
              placement: "point",
              overflow: s.overflow,
              offsetX: this._extractLabelOffset(s, "X"),
              offsetY: this._extractLabelOffset(s, "Y"),
            },
          });
        } else if (s.kind.toLowerCase() === "mark") {
          // 포인트 마커
          const fill = this.hexOrCssToRgba(s.color, s.fillOpacity ?? 1);
          const stroke = this.hexOrCssToRgba(
            s.strokeColor,
            s.strokeOpacity ?? 1,
          );
          symbolizers.push({
            kind: "point",
            mark: {
              shape: s.wellKnownName || s.shape || "Circle",
              radius: s.radius,
            },
            fill: { color: fill },
            stroke: { color: stroke, width: s.strokeWidth },
          });
        } else if (s.kind.toLowerCase() === "fill") {
          // Fill symbolizer → polygon symbolizer로 변환
          const fillColor = this.hexOrCssToRgba(s.color, s.fillOpacity ?? 1);
          const strokeColor = this.hexOrCssToRgba(
            s.outlineColor || s.strokeColor,
            s.strokeOpacity ?? 1,
          );

          symbolizers.push({
            kind: "polygon",
            fill: fillColor ? { color: fillColor } : undefined,
            stroke: strokeColor
              ? {
                  color: strokeColor,
                  width: s.outlineWidth || s.strokeWidth || 1,
                }
              : undefined,
          });
        } else if (s.kind.toLowerCase() === "line") {
          const strokeColor = this.hexOrCssToRgba(
            s.color || s.strokeColor,
            s.strokeOpacity ?? 1,
          );
          symbolizers.push({
            kind: "line",
            stroke: strokeColor
              ? {
                  color: strokeColor,
                  width: s.width || s.strokeWidth || 1,
                  lineDash: s.dashArray || s.lineDash,
                  lineCap: s.lineCap,
                  lineJoin: s.lineJoin,
                }
              : undefined,
          });
        }
      }
      let legendLabel = this._wmsArrayFilterToLegendLabel(rule.filter);
      if (rule.name === "multi_default") legendLabel = "기타";
      const item: CommonStyleItem = {
        when: Object.keys(when).length ? when : undefined,
        style: { symbolizers, name: rule.name, legendLabel: legendLabel },
        order: idx,
      };
      return item;
    });

    return { name, items };
  }

  /** WMS 필터(배열 형태) → {type:"cql", value} | {type:"else"} */
  private static _wmsArrayFilterToCql(arr: any): Filter | undefined {
    if (!arr) return undefined;
    // elseFilter만 오는 케이스
    if (Array.isArray(arr) && arr.length === 1 && arr[0] === "elseFilter") {
      return { type: "else" };
    }
    // 간단 파서: 배열을 재귀로 CQL 문자열 만들기
    const toCql = (node: any): string => {
      if (!Array.isArray(node)) return String(node);
      const op = node[0];
      if (op === "&&" || op === "||") {
        const left = toCql(node[1]);
        const right = toCql(node[2]);
        const opStr = op === "&&" ? "AND" : "OR";
        return `(${left} ${opStr} ${right})`;
      }
      // 비교연산 ["==","field","value"]
      if (["==", "!=", ">", ">=", "<", "<="].includes(op)) {
        const field = node[1];
        const val = node[2];
        const valStr = typeof val === "string" ? `'${val}'` : String(val);
        const opMap: Record<string, string> = { "==": "=", "!=": "<>" };
        return `${field} ${opMap[op] ?? op} ${valStr}`;
      }
      // 알 수 없으면 문자열화
      return String(node);
    };

    try {
      const cql = toCql(arr);
      return { type: "cql", value: cql };
    } catch {
      return undefined;
    }
  }

  private static _extractLabelOffset(
    s: any,
    axis: "X" | "Y",
  ): number | undefined {
    // SLD 구조에서 LabelPlacement.PointPlacement.DisplacementX/Y 꺼내기
    const lp = s?.LabelPlacement?.[0]?.PointPlacement?.[0];
    const d = lp?.Displacement?.[0]?.[`Displacement${axis}`]?.[0];
    const n = d !== undefined ? Number(d) : undefined;
    return Number.isFinite(n) ? (n as number) : undefined;
    // AnchorPointX/Y 등도 필요하면 파싱 가능
  }

  /* ===================== WFS ===================== */

  /** ODF WFS 스타일 객체 (getStyle().getObject()) → CommonStyleSpec */
  static fromWfs(odfWfsStyleObject: any[]): CommonStyleSpec {
    const items: CommonStyleItem[] = [];

    for (let i = 0; i < odfWfsStyleObject.length; i += 1) {
      const entry = odfWfsStyleObject[i];
      const st = entry?.style ?? {};
      const name: string | undefined = st?.name;
      const order: number | undefined =
        entry?.priority ?? entry?.order ?? undefined;

      const symbolizers: Symbolizer[] = [];

      // polygon/line/point 판단은 입력 형식에 따라 달라질 수 있음
      // 여기서는 네가 준 예시처럼 fill+stroke(+text)를 polygon + text로 매핑
      // 1) 도형
      const stroke = this.hex8ToRgba(st?.stroke?.color);
      const strokeWidth = st?.stroke?.width;
      const fill = this.hex8ToRgba(st?.fill?.color);

      // polygon 심볼라이저로 구성 (필요하면 point/line로 분기 추가)
      const polySym: Symbolizer = {
        kind: "polygon",
        fill: fill ? { color: fill } : undefined,
        stroke:
          stroke || strokeWidth
            ? { color: stroke, width: strokeWidth }
            : undefined,
      };
      symbolizers.push(polySym);

      // 2) 텍스트
      if (st?.text) {
        const t = st.text;
        const tFill = this.hex8ToRgba(t?.fill?.color);
        const tStroke = this.hex8ToRgba(t?.stroke?.color);
        const txt: TextStyle = {
          fill: tFill,
          stroke:
            tStroke || t?.stroke?.width
              ? { color: tStroke, width: t?.stroke?.width }
              : undefined,
          fontFamily: this._parseFontFamily(t?.font),
          fontStyle: this._parseFontStyle(t?.font),
          fontWeight: this._parseFontWeight(t?.font),
          fontSize: this._parseFontSize(t?.font),
          placement: (t?.placement as any) ?? "point",
          overflow: t?.overflow,
          offsetX: t?.offsetX,
          offsetY: t?.offsetY,
        };
        symbolizers.push({ kind: "text", text: txt });
      }

      items.push({
        when: undefined, // 필요 시 해상도/필터 붙이면 됨
        style: { symbolizers, name },
        order,
      });
    }

    return { name: "WFS_Style", items };
  }

  /* ===================== 유틸 ===================== */

  /** "#RRGGBB" 또는 "#RRGGBBAA" 또는 CSS Hex + 별도 opacity → RGBA */
  static hexOrCssToRgba(hex?: string, opacity: number = 1): Rgba | undefined {
    if (!hex) return undefined;
    const s = hex.startsWith("#") ? hex.slice(1) : hex;
    const r = parseInt(s.slice(0, 2), 16);
    const g = parseInt(s.slice(2, 4), 16);
    const b = parseInt(s.slice(4, 6), 16);
    const a = s.length >= 8 ? parseInt(s.slice(6, 8), 16) / 255 : 1;
    const finalA = Math.max(0, Math.min(1, a * opacity));
    return [r, g, b, finalA];
  }

  /** "#RRGGBBAA" → RGBA (WFS 예시에 많이 등장) */
  static hex8ToRgba(hex?: string): Rgba | undefined {
    if (!hex) return undefined;
    const s = hex.startsWith("#") ? hex.slice(1) : hex;
    const r = parseInt(s.slice(0, 2), 16);
    const g = parseInt(s.slice(2, 4), 16);
    const b = parseInt(s.slice(4, 6), 16);
    const a = s.length >= 8 ? parseInt(s.slice(6, 8), 16) / 255 : 1;
    return [r, g, b, Math.max(0, Math.min(1, a))];
  }

  /** "normal normal 20px KoPubWorld…" 같은 CSS 폰트 문자열에서 family/size만 대충 파싱 */
  private static _parseFontFamily(font?: string): string | undefined {
    if (!font) return undefined;
    // 마지막 토큰이 family일 확률 높음
    const toks = font.split(/\s+/);
    return toks[toks.length - 1];
  }
  private static _parseFontSize(font?: string): number | undefined {
    if (!font) return undefined;
    const m = font.match(/(\d+(?:\.\d+)?)px/);
    return m ? Number(m[1]) : undefined;
  }
  private static _parseFontStyle(
    font?: string,
  ): "normal" | "italic" | undefined {
    if (!font) return undefined;
    return font.includes("italic") ? "italic" : "normal";
  }
  private static _parseFontWeight(
    font?: string,
  ): "normal" | "bold" | number | undefined {
    if (!font) return undefined;
    if (font.includes("bold")) return "bold";

    const weightMatch = font.match(/\b(100|200|300|400|500|600|700|800|900)\b/);
    if (weightMatch) {
      return Number(weightMatch[1]);
    }

    return "normal";
  }
  private static _wmsArrayFilterToLegendLabel(arr: any): string {
    if (!arr) return "";

    // elseFilter 케이스
    if (Array.isArray(arr) && arr.length === 1 && arr[0] === "elseFilter") {
      return "기타";
    }

    // 배열 형태의 필터를 파싱하여 라벨 생성
    const parseArrayFilter = (node: any): string => {
      if (!Array.isArray(node)) return String(node);

      const [op, left, right] = node;

      // AND 조건: 범위 체크 우선
      if (op === "&&" && Array.isArray(left) && Array.isArray(right)) {
        const [leftOp, leftField, leftVal] = left;
        const [rightOp, rightField, rightVal] = right;

        // 같은 필드의 범위 조건
        if (
          leftField === rightField &&
          [">", ">=", "<", "<="].includes(leftOp) &&
          [">", ">=", "<", "<="].includes(rightOp)
        ) {
          // 범위 조합 체크 (>=,<= | >=,< | >,<= | >,< 등)
          const isRange =
            (leftOp.includes(">") && rightOp.includes("<")) ||
            (leftOp.includes("<") && rightOp.includes(">"));

          if (isRange) {
            const min = leftOp.includes(">") ? leftVal : rightVal;
            const max = rightOp.includes("<") ? rightVal : leftVal;
            return `${min}~${max}`;
          }
        }

        // 일반 AND
        return `${parseArrayFilter(left)} AND ${parseArrayFilter(right)}`;
      }

      // OR 조건
      if (op === "||") {
        return `${parseArrayFilter(left)}, ${parseArrayFilter(right)}`;
      }

      // 비교 조건
      if (["==", "!=", ">", ">=", "<", "<="].includes(op)) {
        return String(right);
      }

      return String(node);
    };
    try {
      return parseArrayFilter(arr);
    } catch (error) {
      console.warn("Failed to parse filter array for legend label:", error);
      return "필터 조건";
    }
  }
}
