import { Textarea } from "@geon-ui/react/primitives/textarea";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import TextareaDemoSource from "!!raw-loader!./demo";

import { TextareaDemo } from "./demo";

const meta = {
  title: "Shadcn/Textarea",
  component: Textarea,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Textarea>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: TextareaDemoSource,
      },
    },
  },
  render: () => <TextareaDemo />,
};
