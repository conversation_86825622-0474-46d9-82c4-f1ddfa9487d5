"use client";

import { createAdminClient } from "@geon-query/model";
import React from "react";

import SearchForm, { SearchOption } from "../../_components/search-form";
import AdminTable from "./_components/admin-table";

export default function Business() {
  const client = createAdminClient();
  const [params, setParams] = React.useState({
    userId: "",
    deptNm: "",
  });

  const searchOptions: SearchOption[] = [
    { label: "아이디", value: "userId" },
    { label: "부서", value: "deptNm" },
  ];

  return (
    <>
      <SearchForm
        options={searchOptions}
        params={params}
        setParams={setParams}
      />
      <AdminTable
        client={client}
        pageIndex={1}
        pageSize={10}
        userId={params.userId}
        deptNm={params.deptNm}
      />
    </>
  );
}
