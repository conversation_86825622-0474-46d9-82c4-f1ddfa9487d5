"use client";

import type { CaptureResult } from "@geon-map/core";
import { useBase<PERSON>ayer, useLayer, useMap } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@geon-ui/react/primitives/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import {
  Camera,
  Download,
  Eye,
  Printer,
  Square,
  StickyNote,
} from "lucide-react";
import * as React from "react";

import type { PaperSize } from "../../types/print-types";
import { PAPER_SIZE_LABELS } from "../../types/print-types";

const { useState, useEffect } = React;

import { usePrint } from "../../hooks";
import type { CaptureType } from "../../types/print-types";
import { ToolbarItem, ToolbarTrigger } from "./base/toolbar-item";

// Props for compound components
export interface ToolbarPrintProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** 캡처 모드 활성화 */
  enableCaptureMode?: boolean;
}

export interface ToolbarPrintTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
}

export interface ToolbarPrintContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export interface ToolbarPrintCaptureOptionProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 캡처 타입 */
  captureType: CaptureType;
  /** 아이콘 */
  icon?: React.ReactNode;
  /** 라벨 */
  label?: string;
}

// Print Context 생성
interface PrintContextValue {
  enableCaptureMode: boolean;
  currentPaperSize: PaperSize;
  captureResult?: CaptureResult | null;
  isCapturing: boolean;
  dialogs: {
    capture: {
      open: boolean;
      openDialog: () => void;
      closeDialog: () => void;
      onOpenChange: (open: boolean) => void;
    };
    paper: {
      open: boolean;
      openDialog: () => void;
      closeDialog: () => void;
      onOpenChange: (open: boolean) => void;
    };
  };
  getPaperSizeInPixels: (
    paperSize: PaperSize,
    dpi?: number,
    includeMargin?: boolean,
    marginMm?: number,
  ) => any;
  startArea: () => void;
  captureCurrentView: () => void;
  startPaperCapture: () => void;
  saveCapturedPng: () => Promise<boolean>;
  saveCapturedPdf: () => Promise<boolean>;
  printCaptured: () => Promise<boolean>;
  saveCanvasPng: (canvas: HTMLCanvasElement) => Promise<boolean>;
  saveCanvasPdf: (canvas: HTMLCanvasElement) => Promise<boolean>;
  printCanvas: (canvas: HTMLCanvasElement) => Promise<boolean>;
  changePaperSize: (size: PaperSize) => void;
  openOriginalPreview: () => void;
}

const PrintContext = React.createContext<PrintContextValue | null>(null);

export const usePrintContext = () => {
  const context = React.useContext(PrintContext);
  if (!context) {
    throw new Error("ToolbarPrint components must be used within ToolbarPrint");
  }
  return context;
};

// Main ToolbarPrint Container
export const ToolbarPrint = React.forwardRef<HTMLDivElement, ToolbarPrintProps>(
  ({ enableCaptureMode = false, className, children, ...props }, ref) => {
    const {
      dialogs,
      currentPaperSize,
      isCapturing,
      captureResult,
      getPaperSizeInPixels,
      startArea,
      captureCurrentView,
      startPaperCapture,
      saveCapturedPng,
      saveCapturedPdf,
      printCaptured,
      saveCanvasPng,
      saveCanvasPdf,
      printCanvas,
      changePaperSize,
      openOriginalPreview,
    } = usePrint({ enableCaptureMode });

    const contextValue = React.useMemo(
      () => ({
        enableCaptureMode,
        currentPaperSize,
        captureResult,
        isCapturing,
        dialogs,
        getPaperSizeInPixels,
        startArea,
        captureCurrentView,
        startPaperCapture,
        saveCapturedPng,
        saveCapturedPdf,
        printCaptured,
        saveCanvasPng,
        saveCanvasPdf,
        printCanvas,
        changePaperSize,
        openOriginalPreview,
      }),
      [
        enableCaptureMode,
        currentPaperSize,
        captureResult,
        isCapturing,
        dialogs,
        getPaperSizeInPixels,
        startArea,
        captureCurrentView,
        startPaperCapture,
        saveCapturedPng,
        saveCapturedPdf,
        printCaptured,
        saveCanvasPng,
        saveCanvasPdf,
        printCanvas,
        changePaperSize,
        openOriginalPreview,
      ],
    );

    return (
      <PrintContext.Provider value={contextValue}>
        <ToolbarItem ref={ref} className={className} {...props}>
          {children}
          <ToolbarPrintPaperDialog />
        </ToolbarItem>
      </PrintContext.Provider>
    );
  },
);

ToolbarPrint.displayName = "ToolbarPrint";

// ToolbarPrintTrigger Component
export const ToolbarPrintTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarPrintTriggerProps
>(
  (
    {
      tooltip = "인쇄",
      size = "default",
      className,
      children,
      onClick,
      ...props
    },
    ref,
  ) => {
    const {
      dialogs: { capture },
    } = usePrintContext();

    const handleClick = React.useCallback(
      (e: React.MouseEvent<HTMLButtonElement>) => {
        capture.openDialog();
        onClick?.(e);
      },
      [capture, onClick],
    );

    return (
      <ToolbarTrigger
        ref={ref}
        tooltip={tooltip}
        size={size}
        className={className}
        onClick={handleClick}
        {...props}
      >
        {children || <Printer className="h-4 w-4" />}
      </ToolbarTrigger>
    );
  },
);

ToolbarPrintTrigger.displayName = "ToolbarPrintTrigger";

// ToolbarPrintContent Component
export const ToolbarPrintContent = React.forwardRef<
  HTMLDivElement,
  ToolbarPrintContentProps
>(({ className, children, ...props }, ref) => {
  const {
    dialogs: { capture },
    captureResult,
  } = usePrintContext();

  return (
    <Dialog open={capture.open} onOpenChange={capture.onOpenChange}>
      <DialogContent ref={ref} className={cn("max-w-md", className)} {...props}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Printer className="w-5 h-5" />
            인쇄 및 캡처
          </DialogTitle>
          <DialogDescription>인쇄 및 캡처 옵션을 선택하세요.</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 캡처 옵션 */}
          <div className="grid grid-cols-3 gap-3">
            <ToolbarPrintCaptureOption captureType="area" />
            <ToolbarPrintCaptureOption captureType="current-view" />
            <ToolbarPrintCaptureOption captureType="paper" />
          </div>

          {/* 미리보기 */}
          <div className="border rounded-lg p-4 bg-gray-50 min-h-32 flex items-center justify-center">
            {captureResult ? (
              <img
                src={captureResult.canvas.toDataURL()}
                alt="캡처된 이미지"
                className="w-full h-auto max-h-48 object-contain rounded"
              />
            ) : (
              <p className="text-center text-gray-500 text-sm">
                캡처할 영역을 선택하세요
              </p>
            )}
          </div>

          {/* 액션 버튼들 */}
          <ToolbarPrintActions />
        </div>

        {children}
      </DialogContent>
    </Dialog>
  );
});

ToolbarPrintContent.displayName = "ToolbarPrintContent";

// ToolbarPrintCaptureOption Component
export const ToolbarPrintCaptureOption = React.forwardRef<
  HTMLButtonElement,
  ToolbarPrintCaptureOptionProps
>(({ captureType, icon, label, className, onClick, ...props }, ref) => {
  const { startArea, captureCurrentView, startPaperCapture } =
    usePrintContext();

  const defaultIcons = {
    area: <Square className="w-6 h-6" />,
    "current-view": <Camera className="w-6 h-6" />,
    paper: <StickyNote className="w-6 h-6" />,
  };

  const defaultLabels = {
    area: "영역 캡처",
    "current-view": "화면 캡처",
    paper: "용지별 캡처",
  };

  const defaultHandlers = {
    area: startArea,
    "current-view": captureCurrentView,
    paper: startPaperCapture,
  };

  const handleClick = React.useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      defaultHandlers[captureType]();
      onClick?.(e);
    },
    [captureType, onClick],
  );

  return (
    <Button
      ref={ref}
      variant="outline"
      className={cn("h-20 flex flex-col items-center gap-2", className)}
      onClick={handleClick}
      {...props}
    >
      {icon || defaultIcons[captureType]}
      <span className="text-sm">{label || defaultLabels[captureType]}</span>
    </Button>
  );
});

ToolbarPrintCaptureOption.displayName = "ToolbarPrintCaptureOption";

// ToolbarPrintActions Component
const ToolbarPrintActions = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const {
    captureResult,
    saveCapturedPng,
    saveCapturedPdf,
    printCaptured,
    openOriginalPreview,
  } = usePrintContext();

  return (
    <div ref={ref} className={cn("space-y-3", className)} {...props}>
      {/* 원본 보기 버튼 */}
      {captureResult && (
        <Button
          className="w-full"
          variant="outline"
          onClick={openOriginalPreview}
        >
          <Eye className="w-4 h-4 mr-2" />
          원본크기로 보기
        </Button>
      )}

      {/* 저장 버튼들 */}
      <div className="flex gap-2">
        <Button
          className="flex-1"
          variant="outline"
          onClick={saveCapturedPng}
          disabled={!captureResult}
        >
          <Download className="w-4 h-4 mr-2" />
          PNG 저장
        </Button>
        <Button
          className="flex-1"
          variant="outline"
          onClick={saveCapturedPdf}
          disabled={!captureResult}
        >
          <Download className="w-4 h-4 mr-2" />
          PDF 저장
        </Button>
      </div>

      {/* 인쇄 버튼 */}
      <Button
        className="w-full"
        onClick={printCaptured}
        disabled={!captureResult}
      >
        <Printer className="w-4 h-4 mr-2" />
        인쇄하기
      </Button>
    </div>
  );
});

ToolbarPrintActions.displayName = "ToolbarPrintActions";

// ToolbarPrintPaperDialog Component
const ToolbarPrintPaperDialog = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>((props, ref) => {
  const {
    dialogs: { paper },
    getPaperSizeInPixels,
    currentPaperSize,
    changePaperSize,
    saveCanvasPng,
    saveCanvasPdf,
    printCanvas,
  } = usePrintContext();



  // TODO: A0(최희라) 테스트용 코드. 맵 컴포넌트 리팩토링 완료 및 a0 프린트 인터페이스 확정 후 수정 필요
  const { odf, zoom, center } = useMap();
  const { layers } = useLayer();
  const { activeOverlayLayers } = useBaseLayer();
  const [map, setMap] = useState<any | null>(null);

  useEffect(() => {
    if (map) {
      const pageDpi = {
        A0: 100,
        A1: 150,
        A2: 180,
        A3: 200,
        A4: 240,
        "A0-L": 100,
        "A1-L": 150,
        "A2-L": 180,
        "A3-L": 200,
        "A4-L": 240,
      };
      const paperPixels = getPaperSizeInPixels(
        currentPaperSize,
        pageDpi[currentPaperSize],
        false,
        0,
      );

      // 1. DOM 컨테이너 크기 설정
      const mapContainer = map.getTarget();
      if (mapContainer) {
        mapContainer.style.width = `${paperPixels.width}px`;
        mapContainer.style.height = `${paperPixels.height}px`;
      }
      // 2. 지도 크기 업데이트
      map.updateSize();
      // activeOverlayLayers.forEach((layer) => {
      //   if (layer.visible) {
      //     layer.odfLayer.clone().setMap(map);
      //   }
      //   layer.odfLayer.clone().setMap(map);
      // });
      layers.forEach((layer) => {
        if (layer.visible) {
          layer.odfLayer.clone().setMap(map);
        }
      });
    }
  }, [currentPaperSize, getPaperSizeInPixels, map]);

  useEffect(() => {
    // Dialog가 열려있고 ODF가 준비되었을 때만 실행
    if (!paper.open || !odf) return;
    // 임시소스
    setTimeout(() => {
      const mapContainer = document.getElementById("toolbar-print-map");
      const coord = new odf.Coordinate(199312.9996, 551784.6924);
      const mapOption = {
        center: center,
        zoom: zoom,
        projection: "EPSG:5186",
        basemap: {},
        pixelRatio: 1,
        optimization: true,
      };
      const map = new odf.Map(mapContainer, mapOption);
      setMap(map);
    }, 1000); // 1000ms 지연
  }, [odf, paper.open]);

  const printMap = async () => {
    if (map) {
      const mapCanvas = document.querySelector(
        "#toolbar-print-map canvas",
      ) as HTMLCanvasElement;
      if (mapCanvas) {
        await printCanvas(mapCanvas);
      }
    }
  };

  const downloadPng = async () => {
    if (map) {
      const mapCanvas = document.querySelector(
        "#toolbar-print-map canvas",
      ) as HTMLCanvasElement;
      if (mapCanvas) {
        await saveCanvasPng(mapCanvas);
      }
    }
  };

  const downloadPdf = async () => {
    if (map) {
      const mapCanvas = document.querySelector(
        "#toolbar-print-map canvas",
      ) as HTMLCanvasElement;
      if (mapCanvas) {
        await saveCanvasPdf(mapCanvas);
      }
    }
  };

  return (
    <Dialog open={paper.open} onOpenChange={paper.onOpenChange}>
      <DialogContent
        className="z-[99999999] p-0 flex flex-col"
        ref={ref}
        style={{
          width: "min(95vw, 1000px)",
          height: "min(95vh, 800px)",
          maxWidth: "none",
          maxHeight: "none",
        }}
        {...props}
      >
        {/* 고정 헤더 */}
        <DialogHeader className="p-6 pb-4 flex-shrink-0 border-b">
          <DialogTitle className="flex items-center justify-between">
            <span>용지별 캡쳐(테스트용)</span>
          </DialogTitle>
          <DialogDescription>
            용지를 선택하고, 지도를 이동해보세요.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-auto p-6">
          <div className="space-y-4">
            {/* 용지 선택 */}
            <div className="flex items-center justify-center gap-3">
              <Select value={currentPaperSize} onValueChange={changePaperSize}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="용지를 선택해주세요." />
                </SelectTrigger>
                <SelectContent className="z-[99999999]">
                  {Object.entries(PAPER_SIZE_LABELS).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="flex space-x-2">
                <Button
                  onClick={printMap}
                  variant="outline"
                  className="flex items-center gap-2 rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                >
                  <Printer className="h-4 w-4" />
                  인쇄
                </Button>

                <Button
                  onClick={downloadPng}
                  variant="outline"
                  className="flex items-center gap-2 rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                >
                  <Download className="h-4 w-4" />
                  PNG 저장
                </Button>

                <Button
                  onClick={downloadPdf}
                  variant="outline"
                  className="flex items-center gap-2 rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                >
                  <Download className="h-4 w-4" />
                  PDF 저장
                </Button>
              </div>
            </div>
            {/* 🎯 지도 미리보기 */}
            <div className="w-auto border rounded">
              {/* 임시 소스 지도 개편된후 수정예정 */}
              <div
                id="toolbar-print-map"
                className="max-h-screen h-screen max-w-screen w-screen"
              ></div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

ToolbarPrintPaperDialog.displayName = "ToolbarPrintPaperDialog";

// ToolbarPrintPaperSizeSelect Component
export const ToolbarPrintPaperSizeSelect = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement>
>(({ className, ...props }, ref) => {
  const { currentPaperSize, changePaperSize } = usePrintContext();

  return (
    <Select value={currentPaperSize} onValueChange={changePaperSize}>
      <SelectTrigger ref={ref} className={cn("w-full", className)} {...props}>
        <SelectValue placeholder="용지를 선택해주세요." />
      </SelectTrigger>
      <SelectContent className="z-[99999999]">
        {Object.entries(PAPER_SIZE_LABELS).map(([key, label]) => (
          <SelectItem key={key} value={key}>
            {label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
});

ToolbarPrintPaperSizeSelect.displayName = "ToolbarPrintPaperSizeSelect";
