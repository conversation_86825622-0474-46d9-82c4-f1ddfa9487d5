import type { API_TYPE, APIConfig, ResponseCode } from "../utils/geonAPI";
import { apiHelper } from "../utils/geonAPI";

const type: API_TYPE = "admin";

type AdminAPIRequest = {
  /** 페이지 사이즈 */
  pageSize: number;
  /** 페이지 번호 */
  pageIndex: number;
  crtfckey?: string;
};

type AdminAPIResponse<T = any> = {
  code: ResponseCode;
  message: string;
  result:
    | string
    | {
        resultList: T[];
        pageInfo?: {
          numOfRows: number;
          pageNo: number;
          totalCount: string;
        };
        errorInfo?: {
          resultCode: string;
          message: string;
        };
        [key: string]: any;
      };
};

interface adminAPIConfig extends APIConfig {}

export type AdminClient = ReturnType<typeof createAdminClient>;

export function createAdminClient(config: adminAPIConfig = {}) {
  const { baseUrl, crtfckey } = config;

  const api = apiHelper<AdminAPIRequest, AdminAPIResponse>({
    type,
    baseUrl,
    crtfckey,
  });

  return {
    author: {
      business: {
        list: api.get<
          AdminAPIRequest & {
            /** 조회할 권한 종류 */
            authorType?: string;
            /** 부서 이름 */
            deptNm?: string;
            /** 사용자 ID */
            userId?: string;
            /** 사용자 이름 */
            userNm?: string;
          }
        >("/author/list"),
        update: api.post<{
          /** 조회할 권한 종류 */
          mergeUserId?: string;
          authorList?: string[];
        }>("/author/merge"),
      },
      menu: api.get<{
        /** 조회할 권한 종류 */
        authorType?: string;
      }>("/author/item/list"),
    },
  };
}
