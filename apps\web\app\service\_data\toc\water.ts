import type { TOCNode } from "@geon-map/react-ui/types";

/**
 * TOC_DATA
 * -------------------------
 * 서비스별 TOC(Tree of Contents) 정의 데이터
 * - 반드시 `TOC_DATA`라는 이름으로 export 해야 함
 * - toc-widget-service.tsx에서 import 하여 사용
 * - 그룹/레이어 구조를 그대로 표현
 *
 * 규칙:
 *  - 모든 서비스별 TOC는 이 형태로 작성 (export const TOC_DATA)
 *  - 레이어 노드는 `type: "layer"`로 지정하고, 실제 layerId는 런타임에서 주입
 *  - 그룹 노드는 `type: "group"`으로 지정하고, children 배열에 하위 노드 정의
 */
export const TOC_DATA: TOCNode[] = [
  {
    id: "service-water",
    name: "상수 관리 시스템",
    type: "group",
    visible: true,
    expanded: true,
    children: [
      {
        id: "service-water-managed",
        name: "관리 대상 레이어",
        type: "group",
        visible: true,
        expanded: true,
        children: [
          {
            id: "muan_gis:wtl_pipe_lm",
            name: "상수관로",
            type: "layer",
            visible: true,
          },
          {
            id: "muan_gis:wtl_sply_ls",
            name: "급수관로",
            type: "layer",
            visible: true,
          },
          {
            id: "muan_gis:wtl_manh_ps",
            name: "상수맨홀",
            type: "layer",
            visible: true,
          },
          {
            id: "muan_gis:wtl_fire_ps",
            name: "소방시설",
            type: "layer",
            visible: true,
          },
          {
            id: "muan_gis:wtl_prga_ps",
            name: "수압계",
            type: "layer",
            visible: true,
          },
          {
            id: "muan_gis:wtl_stpi_ps",
            name: "스탠드파이프",
            type: "layer",
            visible: true,
          },
          {
            id: "muan_gis:wtl_flow_ps",
            name: "유량계",
            type: "layer",
            visible: true,
          },
        ],
      },
      {
        id: "service-water-view",
        name: "조회 대상 레이어",
        type: "group",
        visible: false,
        expanded: false,
        children: [
          {
            id: "muan_gis:wtl_song_ls",
            name: "송수관로",
            type: "layer",
            visible: false,
          },
          {
            id: "muan_gis:wtl_valv_ps",
            name: "변류시설",
            type: "layer",
            visible: false,
          },
          {
            id: "muan_gis:wtl_head_ps",
            name: "수원지",
            type: "layer",
            visible: false,
          },
          {
            id: "muan_gis:wtl_gain_ps",
            name: "취수장",
            type: "layer",
            visible: false,
          },
          {
            id: "muan_gis:wtl_pres_ps",
            name: "가압장",
            type: "layer",
            visible: false,
          },
          {
            id: "muan_gis:wtl_serv_ps",
            name: "배수지",
            type: "layer",
            visible: false,
          },
          {
            id: "muan_gis:wtl_meta_ps",
            name: "급수전 계량기",
            type: "layer",
            visible: false,
          },
          {
            id: "muan_gis:wtl_leak_ps",
            name: "누수지역 및 복구 내역",
            type: "layer",
            visible: false,
          },
          {
            id: "muan_gis:wtl_puri_as",
            name: "정수장",
            type: "layer",
            visible: false,
          },
        ],
      },
    ],
  },
];
