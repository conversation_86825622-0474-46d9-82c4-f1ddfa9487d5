import { HoverCard } from "@geon-ui/react/primitives/hover-card";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import HoverCardDemoSource from "!!raw-loader!./demo";

import { HoverCardDemo } from "./demo";

const meta = {
  title: "Shadcn/HoverCard",
  component: HoverCard,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof HoverCard>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: HoverCardDemoSource,
      },
    },
  },
  render: () => <HoverCardDemo />,
};
