import { Calendar } from "@geon-ui/react/primitives/calendar";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import CalendarDemoSource from "!!raw-loader!./demo";

import { CalendarDemo } from "./demo";

const meta = {
  title: "Shadcn/Calendar",
  component: Calendar,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Calendar>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: CalendarDemoSource,
      },
    },
  },
  render: () => <CalendarDemo />,
};
