import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import ChartDemoSource from "!!raw-loader!./demo";

import { ChartDemo } from "./demo";

const meta = {
  title: "Shadcn/Chart",
  component: ChartDemo,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ChartDemo>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: ChartDemoSource,
      },
    },
  },
  render: () => <ChartDemo />,
};
