import { Input } from "@geon-ui/react/primitives/input";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import InputDemoSource from "!!raw-loader!./demo";

import { InputDemo } from "./demo";

const meta = {
  title: "Shadcn/Input",
  component: Input,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Input>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: InputDemoSource,
      },
    },
  },
  render: () => <InputDemo />,
};
