import { Command } from "@geon-ui/react/primitives/command";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import CommandDemoSource from "!!raw-loader!./demo";

import { CommandDemo } from "./demo";

const meta = {
  title: "Shadcn/Command",
  component: Command,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Command>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: CommandDemoSource,
      },
    },
  },
  render: () => <CommandDemo />,
};
