import { Collapsible } from "@geon-ui/react/primitives/collapsible";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import CollapsibleDemoSource from "!!raw-loader!./demo";

import { CollapsibleDemo } from "./demo";

const meta = {
  title: "Shadcn/Collapsible",
  component: Collapsible,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Collapsible>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: CollapsibleDemoSource,
      },
    },
  },
  render: () => <CollapsibleDemo />,
};
