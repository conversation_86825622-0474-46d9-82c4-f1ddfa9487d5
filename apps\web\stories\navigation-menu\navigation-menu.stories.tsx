import { NavigationMenu } from "@geon-ui/react/primitives/navigation-menu";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import NavigationMenuDemoSource from "!!raw-loader!./demo";

import { NavigationMenuDemo } from "./demo";

const meta = {
  title: "Shadcn/NavigationMenu",
  component: NavigationMenu,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof NavigationMenu>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: NavigationMenuDemoSource,
      },
    },
  },
  render: () => <NavigationMenuDemo />,
};
