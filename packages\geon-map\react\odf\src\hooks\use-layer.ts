/** 미완성 상태
 * visible, opacity, zIndex, style 등을 변경했을 때 store 상태가 올바르게 업데이트 되는가에 대한 확인이 필요합니다.
 * 이 외에도 앱 개발시 필요한 상태가 누락되거나 불필요한 type 이 정의되어 있을 수 있습니다.

 * 지금은 임의로 막 넣었는데 실제로 사용할 때 args 를 어떻게 받아야 (특히 addLayer 같은) 쉽게 사용할 수 있을지?
 * odf.layerFactory.produce 를 호출하기위한 helper 메서드도 다른파일에 분리해야하고
 * useLayer 가 제공하는 메서드가 너무많은데, 주로 사용하는 것만 노출한다거나,
 * 상태변경 함수의 집합은 useLayerActions 로 분리한다거나
 * 레이어의 렌더링 여부같은 상태도 추가해준다거나..
 * 이 외에도 생각나는 것이 있다면 즉시 의논하면서 구조를 잘 정립해야한다고 생각합니다.
 */

"use client";

import { CommonStyle, MapProjection } from "@geon-map/core";
import { useCallback } from "react";

import { useStores } from "../contexts/map-store-context";
import type {
  AddFeatureOptions,
  Layer,
  LayerProps,
} from "../types/layer-types";
import { useMap } from "./use-map";

/**
 * Layer 관리 훅의 반환 타입
 */
export interface UseLayerReturn {
  /** 모든 레이어 목록 */
  layers: Layer[];

  /** Draw 레이어 (단일) */
  drawLayer: Layer | null;

  /** Measure 레이어 (단일) */
  measureLayer: Layer | null;

  /** Clear 레이어 (단일) */
  clearLayer: Layer | null;

  /** ID로 Store에서 레이어 찾기 */
  getLayerById: (layerId: string) => Layer | null;

  /** ID로 ODF 레이어 찾기 (Core 위임) */
  getODFLayerById: (layerId: string) => any | null;

  /** 타입으로 레이어 찾기 */
  getLayerByType: (type: "draw" | "measure" | "clear") => Layer | null;

  /** 레이어 ID로 타입 확인 */
  getLayerTypeById: (layerId: string) => "draw" | "measure" | "clear" | "other";

  /** Draw 레이어 추가/업데이트 */
  setDrawLayer: (
    drawLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;

  /** Measure 레이어 추가/업데이트 */
  setMeasureLayer: (
    measureLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;

  /** Clear 레이어 추가/업데이트 */
  setClearLayer: (
    clearLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;

  /** 레이어 가시성 토글 */
  toggleLayerVisibility: (layerId: string) => void;

  /** 레이어 제거 (Core 위임) */
  removeLayer: (layerId: string) => void;

  // === 일반 레이어 관리 ===
  /** 일반 레이어 추가 (Geoserver, GeoJSON 등) */
  addLayer: (props: LayerProps) => Promise<string | void>;

  /** 일반 레이어들 추가 (Geoserver, GeoJSON 등) */
  addLayers: (props: LayerProps[]) => Promise<string[] | void>;

  /** 레이어 스타일 업데이트 */
  updateStyle: (layerId: string, style: any) => void;

  /** 레이어 가시성 설정 */
  setVisible: (layerId: string, visible: boolean) => void;

  /** 레이어 Z-Index 설정 */
  setZIndex: (layerId: string, zIndex: number) => void;

  /** 레이어 투명도 설정 */
  setOpacity: (layerId: string, opacity: number) => void;

  /** 레이어 투명도 가져오기 */
  getOpacity: (layerId: string) => number | null;

  /** 레이어에 맞춰 지도 확대/축소 */
  fitToLayer: (layerId: string, duration?: number) => boolean;

  /** 레이어를 최상위로 이동 */
  setMaxZIndex: (layerId: string) => void;

  /** 레이어 스타일 업데이트 (확장) */
  updateLayerStyle: (layerId: string, style: any) => boolean;

  /** 레이어에 피처 추가 */
  addFeature: (
    layerId: string,
    feature: any,
    options?: AddFeatureOptions,
  ) => boolean;

  /** 레이어의 모든 피처 제거 */
  clearFeatures: (layerId: string) => boolean;

  // === ODF 표준 메서드 ===
  /** 여러 피처 추가 */
  addFeatures: (
    layerId: string,
    features: any[],
    options?: AddFeatureOptions,
  ) => boolean;

  /** 피처 제거 */
  removeFeature: (layerId: string, feature: any) => boolean;

  /** ID로 피처 제거 */
  removeFeatureById: (layerId: string, featureId: string | number) => boolean;

  /** 모든 피처 조회 */
  getFeatures: (layerId: string) => any[];

  /** ID로 피처 조회 */
  getFeatureById: (layerId: string, featureId: string | number) => any | null;

  /** KML로 내보내기 */
  toKML: (layerId: string, downloadFile?: boolean) => string | null;

  /** GeoJSON으로 내보내기 */
  toGeoJson: (layerId: string) => any | null;

  /** KML에서 가져오기 */
  fromKML: (
    layerId: string,
    kml: string,
    dataProjectionCode?: string,
    featureProjectionCode?: string,
  ) => boolean;

  /** GeoJSON에서 가져오기 */
  fromGeoJson: (
    layerId: string,
    geoJson: any,
    dataProjectionCode?: string,
    featureProjectionCode?: string,
  ) => boolean;
  /** 레이어 범례 URL 조회 */
  getLegendUrl: (
    layerId: string,
    options?: GetLegendGraphicOptions,
  ) => string | null;

  // === 레이어 선택 관리 ===
  /** 선택된 레이어 ID */
  selectedLayerId?: string;

  /** 레이어 선택 */
  selectLayer: (layerId: string) => void;

  /** 확장된 그룹 목록 */
  expandedGroups: Set<string>;

  /** 그룹 토글 */
  toggleGroup: (groupId: string) => void;
  /** 레이어 찾기 */
  findLayer: (layerId: string) => Layer | undefined;

  /** 레이어 스타일 객체 조회 */
  getLayerStyleObject: (layerId: string) => Promise<any | null>;
}

// packages/geon-map/react/odf/src/types/legend-types.ts
export interface GetLegendGraphicOptions {
  /** 이미지 너비 (픽셀) */
  width?: number;
  /** 이미지 높이 (픽셀) */
  height?: number;
  /** 이미지 포맷 */
  format?: "image/png" | "image/jpeg" | "image/gif";
  /** 스타일 이름 */
  style?: string;
  /** 특정 규칙 이름 */
  rule?: string;
  /** 축척 값 */
  scale?: number;
  /** SLD 문서 URL */
  sld?: string;
  /** SLD 문서 내용 */
  sld_body?: string;
  /** 범례 옵션 (폰트, 색상 등) */
  legend_options?: {
    fontName?: string;
    fontSize?: number;
    fontColor?: string;
    bgColor?: string;
    transparent: boolean;
    fontAntiAliasing?: boolean;
    forceLabels?: "on" | "off";
    forceTitles?: "on" | "off";
    dx?: number;
    dy?: number;
    mx?: number;
    my?: number;
    borderColor?: string;
    border?: boolean;
    [key: string]: any;
  };
}

/**
 * 통합 Layer 관리 훅
 *
 * @returns Layer 관리 관련 상태와 함수들
 *
 * @example
 * ```typescript
 * function FeatureManager() {
 *   const {
 *     drawLayer,
 *     measureLayer,
 *     getLayerTypeById,
 *     setDrawLayer,
 *     addLayer
 *   } = useLayer();
 *
 *   // Draw 레이어 설정
 *   useEffect(() => {
 *     if (drawCore) {
 *       const layer = drawCore.getDrawLayer();
 *       setDrawLayer(layer, { name: 'My Draw Layer' });
 *     }
 *   }, [drawCore, setDrawLayer]);
 *
 *   // 일반 레이어 추가
 *   const handleAddGeoserver = async () => {
 *     await addLayer({
 *       type: 'geoserver',
 *       server: 'http://example.com/geoserver',
 *       layer: 'workspace:layer',
 *       service: 'wms'
 *     });
 *   };
 *
 *   return <div>Layer management UI</div>;
 * }
 * ```
 */
export function useLayer(): UseLayerReturn {
  const { map, odf, isLoading } = useMap();
  const { layerStore } = useStores();

  // LayerStore에서 상태와 액션 가져오기
  const layers = layerStore((state) => state.layers);
  const selectedLayerId = layerStore((state: any) => state.selectedLayerId);
  const expandedGroups = layerStore((state: any) => state.expandedGroups);
  const getLayerByType = layerStore((state: any) => state.getLayerByType);
  const getLayerById = layerStore((state: any) => state.getLayerById);
  const getLayerTypeById = layerStore((state: any) => state.getLayerTypeById);
  const addDrawLayer = layerStore((state: any) => state.addDrawLayer);
  const addMeasureLayer = layerStore((state: any) => state.addMeasureLayer);
  const addClearLayer = layerStore((state: any) => state.addClearLayer);
  const toggleLayerVisibility = layerStore(
    (state: any) => state.toggleLayerVisibility,
  );
  const removeLayerFromStore = layerStore((state: any) => state.removeLayer);
  const addLayerToStore = layerStore((state: any) => state.addLayer);
  const updateLayerInStore = layerStore((state: any) => state.updateLayer);
  const setSelectedLayer = layerStore((state: any) => state.setSelectedLayer);
  const toggleGroup = layerStore((state: any) => state.toggleGroup);

  const layerFactory = layerStore((state: any) => state.layerFactory);

  // 개별 레이어 상태 (computed)
  const drawLayer = getLayerByType("draw");
  const measureLayer = getLayerByType("measure");
  const clearLayer = getLayerByType("clear");

  // 레이어 설정 헬퍼 함수들
  const setDrawLayer = useCallback(
    (drawLayer: any, options?: { name?: string; visible?: boolean }) => {
      addDrawLayer(drawLayer, options);
    },
    [addDrawLayer],
  );

  const setMeasureLayer = useCallback(
    (measureLayer: any, options?: { name?: string; visible?: boolean }) => {
      addMeasureLayer(measureLayer, options);
    },
    [addMeasureLayer],
  );

  const setClearLayer = useCallback(
    (clearLayer: any, options?: { name?: string; visible?: boolean }) => {
      addClearLayer(clearLayer, options);
    },
    [addClearLayer],
  );

  // === 일반 레이어 관리 함수들 ===
  const addLayer = useCallback(
    async (props: LayerProps): Promise<string | void> => {
      if (!layerFactory) {
        console.warn("[addLayer] LayerFactory not ready");
        return;
      }

      console.log(`[addLayer] 🎯 Creating layer:`, props);

      try {
        // 🚀 Core LayerFactory가 모든 복잡한 처리를 담당
        const result = await layerFactory.createLayer(props);

        if (result.success && result.layer) {
          const config = result.layer.getConfig();

          // 🎯 스토어에 추가 (AbstractLayer 인스턴스 포함)
          const storeLayer: Layer = {
            id: result.layer.getId(),
            name: config.name || result.layer.getId(),
            type: config.type as any,
            visible: config.visible ?? true,
            zIndex: config.zIndex ?? 2, // 기본값을 2로 설정 (배경지도보다 위에)
            odfLayer: result.layer.getODFLayer(),
            abstractLayer: result.layer, // AbstractLayer 인스턴스 저장
            params: config,
            style: props.renderOptions?.style,
          };

          addLayerToStore(storeLayer);

          if (result.type === "duplicate") {
            console.log(
              `[addLayer] 🔄 Layer already exists: ${result.layer.getId()}`,
            );
          } else {
            console.log(`[addLayer] ✅ Layer created: ${result.layer.getId()}`);
          }

          return result.layer.getId();
        } else {
          console.error(`[addLayer] ❌ Failed to create layer:`, result.error);
          return;
        }
      } catch (error) {
        console.error(`[addLayer] ❌ Layer creation error:`, error);
        return;
      }
    },
    [layerFactory, addLayerToStore],
  );

  const removeLayer = useCallback(
    (layerId: string) => {
      // Store에서 레이어 찾아서 제거
      const layer = layers.find((l: any) => l.id === layerId);
      if (layer?.odfLayer && map) {
        map.removeLayer(layer.odfLayer);
        removeLayerFromStore(layerId);
      }
    },
    [layers, map, layerFactory, removeLayerFromStore],
  );

  const updateStyle = useCallback(
    (layerId: string, style: any) => {
      const currentLayers = layerStore.getState().layers;
      const layer = currentLayers.find((l: any) => l.id === layerId);
      if (!layer) return;

      try {
        const parsedStyle =
          typeof style === "string" ? JSON.parse(style) : style;

        if (layer.type === "geoserver" && layer.params.service === "wms") {
          const sldStyle = odf.StyleFactory.produceSLD(parsedStyle);
          layer.odfLayer?.setSLD?.(sldStyle);
        } else {
          const odfStyle = odf.StyleFactory.produce(parsedStyle);
          layer.odfLayer?.setStyle?.(odfStyle);
        }
        const commonStyleSpec = CommonStyle.fromOdfLayer(layer.odfLayer);

        updateLayerInStore(layerId, {
          style: parsedStyle,
          commonStyleSpec: commonStyleSpec,
        });
      } catch (error) {
        console.error("Failed to update layer style:", error);
      }
    },
    [odf, updateLayerInStore],
  );

  const setVisible = useCallback(
    (layerId: string, visible: boolean) => {
      // Store에서 AbstractLayer 인스턴스 찾아서 호출
      const layer = layers.find((l: any) => l.id === layerId);
      console.log(`setVisible: ${layerId} -> ${visible}`);
      console.log(`layers:`, layers);
      console.log(`layer:`, layer);
      if (layer?.abstractLayer) {
        console.log(`setVisible: ${layerId} -> ${visible}`);
        layer.abstractLayer.setVisible(visible);
        updateLayerInStore(layerId, { visible });
      }
    },
    [layers, updateLayerInStore],
  );

  const setZIndex = useCallback(
    (layerId: string, zIndex: number) => {
      // Store에서 레이어 찾기
      const layer = layers.find((l: any) => l.id === layerId);
      if (!layer) {
        return;
      }

      // AbstractLayer를 통한 설정 시도
      if (layer.abstractLayer) {
        try {
          layer.abstractLayer.setZIndex(zIndex);
        } catch (error) {
          console.error(
            `[setZIndex] ❌ AbstractLayer setZIndex failed for ${layerId}:`,
            error,
          );
        }
      }

      // ODF Layer 직접 설정 (fallback)
      // if (layer.odfLayer && layer.odfLayer.setZIndex) {
      //   try {
      //     layer.odfLayer.setZIndex(zIndex);
      //     console.log(
      //       `[setZIndex] ✅ ODF Layer z-index set: ${layerId} -> ${zIndex}`,
      //     );
      //   } catch (error) {
      //     console.error(
      //       `[setZIndex] ❌ ODF Layer setZIndex failed for ${layerId}:`,
      //       error,
      //     );
      //   }
      // } else if (layer.odfLayer) {
      //   console.warn(
      //     `[setZIndex] ⚠️ ODF Layer does not support setZIndex: ${layerId}`,
      //   );
      // }

      // Store 업데이트
      updateLayerInStore(layerId, { zIndex });
    },
    [layers, updateLayerInStore],
  );

  const setOpacity = useCallback(
    (layerId: string, opacity: number) => {
      // Store에서 AbstractLayer 인스턴스 찾아서 호출
      const layer = layers.find((l: any) => l.id === layerId);
      if (layer?.abstractLayer) {
        layer.abstractLayer.setOpacity(opacity);
        updateLayerInStore(layerId, { opacity });
      }
    },
    [layers, updateLayerInStore],
  );

  const getOpacity = useCallback(
    (layerId: string) => {
      // Store에서 AbstractLayer 인스턴스 찾아서 호출
      const layer = layers.find((l: any) => l.id === layerId);
      return layer?.abstractLayer?.getOpacity() ?? null;
    },
    [layers],
  );

  const fitToLayer = useCallback(
    (layerId: string, duration: number = 0): boolean => {
      // Store에서 AbstractLayer 인스턴스 찾아서 호출
      const layer = layers.find((l: any) => l.id === layerId);
      if (layer?.abstractLayer) {
        layer.abstractLayer.fit(duration);
        return true;
      }
      return false;
    },
    [layers],
  );

  const setMaxZIndex = useCallback(
    (layerId: string) => {
      const currentLayers = layerStore.getState().layers;
      const layer = currentLayers.find((l: any) => l.id === layerId);
      if (layer) {
        map?.setZIndex(layerId, map.getMaxZIndex());
      }
    },
    [map],
  );

  const updateLayerStyle = useCallback(
    (layerId: string, style: any) => {
      const currentLayers = layerStore.getState().layers;
      const layer = currentLayers.find((l: any) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없음 (ID: ${layerId})`);
        return false;
      }

      try {
        const parsedStyle =
          typeof style === "string" ? JSON.parse(style) : style;

        if (layer.type === "geoserver" && layer.params.service === "wms") {
          const sldStyle = odf.StyleFactory.produceSLD(parsedStyle);
          layer.odfLayer.setSLD(sldStyle);
        } else {
          layer.odfLayer.setStyle(parsedStyle);
        }
        const commonStyleSpec = CommonStyle.fromOdfLayer(layer.odfLayer);

        updateLayerInStore(layerId, {
          style: parsedStyle,
          commonStyleSpec: commonStyleSpec,
        });
        return true;
      } catch (error) {
        console.error(`스타일 업데이트 중 오류 (ID: ${layerId}):`, error);
        return false;
      }
    },
    [odf, updateLayerInStore],
  );

  const addFeature = useCallback(
    (
      layerId: string,
      feature: any,
      options: AddFeatureOptions = {},
    ): boolean => {
      const currentLayers = layerStore.getState().layers;
      console.log(currentLayers);
      const layer = currentLayers.find((l: any) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return false;
      }
      let targetFeature = feature;

      const vectorLayerTypes = ["empty", "geojson", "kml", "csv"];
      if (!vectorLayerTypes.includes(layer.type)) {
        console.error(
          `피쳐 추가는 벡터 레이어에서만 가능합니다. 현재 레이어 타입: ${layer.type}`,
        );
        return false;
      }

      try {
        const { srid } = options;
        if (srid) {
          const mapProjection = new MapProjection(map);
          targetFeature = mapProjection.projectGeom(feature, srid);
        }

        layer.odfLayer.addFeature(targetFeature);
        return true;
      } catch (error) {
        console.error(`피쳐 추가 중 오류 발생 (레이어 ID: ${layerId}):`, error);
        return false;
      }
    },
    [map],
  );

  const clearFeatures = useCallback(
    (layerId: string): boolean => {
      // Store에서 AbstractLayer 인스턴스 찾아서 호출
      const layer = layers.find((l: any) => l.id === layerId);
      if (layer?.abstractLayer) {
        layer.abstractLayer.clearFeatures();
        return true;
      }
      return false;
    },
    [layers],
  );

  const findLayer = useCallback(
    (layerId: string) => {
      return layers.find((l: any) => l.id === layerId);
    },
    [layers],
  );

  // === ODF 표준 메서드 구현 ===
  const addFeatures = useCallback(
    (
      layerId: string,
      features: any[],
      options: AddFeatureOptions = {},
    ): boolean => {
      const currentLayers = layerStore.getState().layers;
      const layer = currentLayers.find((l: any) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return false;
      }

      const vectorLayerTypes = [
        "empty",
        "geojson",
        "kml",
        "csv",
        "draw",
        "measure",
      ];
      if (!vectorLayerTypes.includes(layer.type)) {
        console.error(
          `피쳐 추가는 벡터 레이어에서만 가능합니다. 현재 레이어 타입: ${layer.type}`,
        );
        return false;
      }

      try {
        const { srid } = options;
        let targetFeatures = features;

        if (srid) {
          const mapProjection = new MapProjection(map);
          targetFeatures = features.map((feature) =>
            mapProjection.projectGeom(feature, srid),
          );
        }

        // ODF Layer의 addFeatures 메서드 사용 (있는 경우) 또는 개별 추가
        if (layer.odfLayer.addFeatures) {
          layer.odfLayer.addFeatures(targetFeatures);
        } else {
          targetFeatures.forEach((feature) => {
            layer.odfLayer.addFeature(feature);
          });
        }
        return true;
      } catch (error) {
        console.error(`피쳐 추가 중 오류 발생 (레이어 ID: ${layerId}):`, error);
        return false;
      }
    },
    [map],
  );

  const removeFeature = useCallback(
    (layerId: string, feature: any): boolean => {
      const currentLayers = layerStore.getState().layers;
      const layer = currentLayers.find((l: any) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return false;
      }

      try {
        layer.odfLayer.removeFeature(feature);
        return true;
      } catch (error) {
        console.error(`피쳐 제거 중 오류 발생 (레이어 ID: ${layerId}):`, error);
        return false;
      }
    },
    [],
  );

  const removeFeatureById = useCallback(
    (layerId: string, featureId: string | number): boolean => {
      const currentLayers = layerStore.getState().layers;
      const layer = currentLayers.find((l: any) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return false;
      }

      try {
        // ODF Layer의 removeFeatureById 메서드 사용 (있는 경우)
        if (layer.odfLayer.removeFeatureById) {
          layer.odfLayer.removeFeatureById(featureId);
        } else {
          // 대안: 피처를 찾아서 제거
          const feature = layer.odfLayer.getFeatureById?.(featureId);
          if (feature) {
            layer.odfLayer.removeFeature(feature);
          } else {
            console.warn(`피쳐를 찾을 수 없습니다 (ID: ${featureId})`);
            return false;
          }
        }
        return true;
      } catch (error) {
        console.error(`피쳐 제거 중 오류 발생 (레이어 ID: ${layerId}):`, error);
        return false;
      }
    },
    [],
  );

  const getFeatures = useCallback((layerId: string): any[] => {
    const currentLayers = layerStore.getState().layers;
    const layer = currentLayers.find((l: any) => l.id === layerId);
    if (!layer) {
      console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
      return [];
    }

    try {
      return layer.odfLayer.getFeatures?.() || [];
    } catch (error) {
      console.error(`피쳐 조회 중 오류 발생 (레이어 ID: ${layerId}):`, error);
      return [];
    }
  }, []);

  const getFeatureById = useCallback(
    (layerId: string, featureId: string | number): any | null => {
      const currentLayers = layerStore.getState().layers;
      const layer = currentLayers.find((l: any) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return null;
      }

      try {
        return layer.odfLayer.getFeatureById?.(featureId) || null;
      } catch (error) {
        console.error(`피쳐 조회 중 오류 발생 (레이어 ID: ${layerId}):`, error);
        return null;
      }
    },
    [],
  );

  const toKML = useCallback(
    (layerId: string, downloadFile?: boolean): string | null => {
      const currentLayers = layerStore.getState().layers;
      const layer = currentLayers.find((l: any) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return null;
      }

      try {
        return layer.odfLayer.toKML?.(downloadFile) || null;
      } catch (error) {
        console.error(
          `KML 내보내기 중 오류 발생 (레이어 ID: ${layerId}):`,
          error,
        );
        return null;
      }
    },
    [],
  );

  const toGeoJson = useCallback((layerId: string): any | null => {
    const currentLayers = layerStore.getState().layers;
    const layer = currentLayers.find((l: any) => l.id === layerId);
    if (!layer) {
      console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
      return null;
    }

    try {
      return layer.odfLayer.toGeoJson?.() || null;
    } catch (error) {
      console.error(
        `GeoJSON 내보내기 중 오류 발생 (레이어 ID: ${layerId}):`,
        error,
      );
      return null;
    }
  }, []);

  const fromKML = useCallback(
    (
      layerId: string,
      kml: string,
      dataProjectionCode?: string,
      featureProjectionCode?: string,
    ): boolean => {
      const currentLayers = layerStore.getState().layers;
      const layer = currentLayers.find((l: any) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return false;
      }

      try {
        return (
          layer.odfLayer.fromKML?.(
            kml,
            dataProjectionCode,
            featureProjectionCode,
          ) || false
        );
      } catch (error) {
        console.error(
          `KML 가져오기 중 오류 발생 (레이어 ID: ${layerId}):`,
          error,
        );
        return false;
      }
    },
    [],
  );

  const fromGeoJson = useCallback(
    (
      layerId: string,
      geoJson: any,
      dataProjectionCode?: string,
      featureProjectionCode?: string,
    ): boolean => {
      const currentLayers = layerStore.getState().layers;
      const layer = currentLayers.find((l: any) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return false;
      }

      try {
        return (
          layer.odfLayer.fromGeoJson?.(
            geoJson,
            dataProjectionCode,
            featureProjectionCode,
          ) || false
        );
      } catch (error) {
        console.error(
          `GeoJSON 가져오기 중 오류 발생 (레이어 ID: ${layerId}):`,
          error,
        );
        return false;
      }
    },
    [],
  );

  const addLayers = useCallback(
    async (propsList: LayerProps[]) => {
      const layerIds = await Promise.all(
        propsList.map((props) => addLayer(props)),
      );
      return layerIds.filter((id) => id) as string[];
    },
    [addLayer],
  );

  const getLayerStyleObject = useCallback(
    async (layerId: string) => {
      const layer = layers.find((l: any) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return null;
      }
      return layer.odfLayer.getStyle()?.getObject();
    },
    [layers],
  );

  const getLegendUrl = useCallback(
    (layerId: string, options?: GetLegendGraphicOptions): string | null => {
      // Store에서 AbstractLayer 인스턴스 찾아서 호출
      const layer = layers.find((l: any) => l.id === layerId);
      return layer?.abstractLayer?.getLegendUrl(options) ?? null;
    },
    [layers],
  );

  return {
    // 상태
    layers,
    drawLayer,
    measureLayer,
    clearLayer,
    selectedLayerId,
    expandedGroups,

    // Draw/Measure/Clear 레이어 설정 함수
    setDrawLayer,
    setMeasureLayer,
    setClearLayer,

    // 일반 레이어 관리 함수
    addLayer,
    addLayers,
    updateStyle,
    setVisible,
    setZIndex,
    setOpacity,
    fitToLayer,
    setMaxZIndex,
    updateLayerStyle,
    addFeature,
    clearFeatures,

    // ODF 표준 메서드
    addFeatures,
    removeFeature,
    removeFeatureById,
    getFeatures,
    getFeatureById,
    getOpacity,
    toKML,
    toGeoJson,
    fromKML,
    fromGeoJson,
    getLegendUrl,

    // 공통 조회/관리 함수 (Store 기반)
    getLayerById,
    getLayerByType,
    getLayerTypeById,
    findLayer,

    // ODF 레이어 조회 (react-odf에서 유효성 검증)
    getODFLayerById: useCallback(
      (layerId: string) => {
        const layer = layers.find((l: any) => l.id === layerId);
        return layer?.odfLayer ?? null;
      },
      [layers],
    ),

    // 레이어 관리
    toggleLayerVisibility,
    removeLayer,
    selectLayer: setSelectedLayer,
    toggleGroup,
    getLayerStyleObject,
  };
}

/**
 * 레이어 ODF ID 추출 헬퍼
 *
 * @param layer - Layer 인터페이스 객체
 * @returns ODF Layer ID 또는 null
 */
export function getODFLayerId(layer: Layer | null): string | null {
  if (!layer?.odfLayer) return null;

  try {
    return layer.odfLayer.getODFId?.() || null;
  } catch (error) {
    console.error("Failed to get ODF Layer ID:", error);
    return null;
  }
}

/**
 * 레이어 타입 판별 헬퍼 (ODF ID 기반)
 *
 * @param odfLayerId - ODF Layer ID
 * @returns 레이어 타입
 */
export function determineLayerTypeByODFId(
  odfLayerId: string,
): "draw" | "measure" | "clear" | "other" {
  if (!odfLayerId) return "other";

  const lowerCaseId = odfLayerId.toLowerCase();

  if (lowerCaseId.includes("draw")) return "draw";
  if (lowerCaseId.includes("measure")) return "measure";
  if (lowerCaseId.includes("clear")) return "clear";

  return "other";
}
