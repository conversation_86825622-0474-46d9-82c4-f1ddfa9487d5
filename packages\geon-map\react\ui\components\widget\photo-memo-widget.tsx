import { Marker } from "@geon-map/core";
import { useDraw, useFeatureActions, useMap } from "@geon-map/react-odf";
import { createGeonMagpClient } from "@geon-query/model";
import { useAppMutation } from "@geon-query/react-query";
import { Button } from "@geon-ui/react/primitives/button";
import { Input } from "@geon-ui/react/primitives/input";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@geon-ui/react/primitives/sheet";
import { PlusIcon } from "lucide-react";
import { useEffect, useRef, useState } from "react";

import MapMarkerIcon from "../../resource/images/map-marker.png";

type FormState = {
  photoMemoId: string;
  sj: string;
  cn: string;
  userMapScopeValue: string;
  atchmnflId: string;
  registerId: string;
  registDt: string;
  updusrId: string;
  updtDt: string;
};

const photoMemoList = [
  {
    photoMemoId: "user_map_1",
    sj: "오송산업단지 약도",
    cn: "20250922_오송산업단지 약도 이미지",
    userMapScopeValue: "163112.56486329937,265862.9307673176",
    atchmnflId: "AT0000000027",
    registerId: "admin",
    registDt: "20250922095733",
    updusrId: "",
    updtDt: "",
  },
  {
    photoMemoId: "user_map_2",
    sj: "오송산업단지 홍보영상",
    cn: "20250922_오송산업단지 홍보영상",
    userMapScopeValue: "165861.21745639603,260823.73434664035",
    atchmnflId: "AT0000000027",
    registerId: "admin",
    registDt: "20250922095832",
    updusrId: "",
    updtDt: "",
  },
];

const registerId = "admin";

export const PhotoMemoWidget = () => {
  const { map, odf } = useMap();
  const { startDrawing } = useDraw();
  const { deleteFeature } = useFeatureActions();

  const [isOpen, setIsOpen] = useState(false);

  const initialForm: FormState = {
    photoMemoId: "",
    sj: "",
    cn: "",
    userMapScopeValue: "",
    atchmnflId: "",
    registerId: registerId,
    registDt: "",
    updusrId: "",
    updtDt: "",
  };
  const [form, setForm] = useState(initialForm);

  const textField: { key: keyof FormState; label: string }[] = [
    { key: "sj", label: "제목" },
    { key: "cn", label: "내용" },
  ];

  // 기존 지도 마커 표시
  useEffect(() => {
    photoMemoList.map((pm) =>
      Marker.createAndAddMarker(map, {
        position: new odf.Coordinate(pm.userMapScopeValue.split(",")),
        draggable: false,
        style: { src: MapMarkerIcon.src, height: "50px", width: "50px" },
      }),
    );
  });

  // 신규 지도 마커 표시
  const handleToDrawing = (isEdit: boolean = false) => {
    setIsOpen(false);
    const { drawend } = startDrawing("point");
    drawend((feature) => {
      const projection = map.getProjection?.();
      const clone = feature.clone();
      const transformedFeature = projection
        ? projection.unprojectGeom(clone, "5186")
        : clone;

      const centerPoint = transformedFeature.getCenterPoint();
      console.log(centerPoint);
      const marker = Marker.createAndAddMarker(map, {
        position: new odf.Coordinate(centerPoint),
        offset: [0, 10],
        draggable: false,
        style: { src: MapMarkerIcon.src, height: "50px", width: "50px" },
      });

      if (marker) {
        if (isEdit) {
          // 수정
          setForm({ ...form, userMapScopeValue: centerPoint });
        } else {
          // 등록
          setForm({ ...initialForm, userMapScopeValue: centerPoint });
        }
        setIsOpen(true);
        deleteFeature(feature, "draw");
      }
    });
  };

  // 파일 업로드 API
  const client = createGeonMagpClient();
  const uploadMutation = useAppMutation({
    mutationFn: async (file: File) =>
      client.attachment.upload({ registerId }, [
        { file, field: "attachments" },
      ]),
    onSuccess: (res) => {
      setForm({ ...form, atchmnflId: res.result.atchmnflId });
    },
    onError: (err: any) => {
      console.log(err);
    },
  });

  // 이미지 파일 선택
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    uploadMutation.mutate(file);
  };

  // 신규 사진/메모 등록
  const handleToRegister = () => {
    setForm(initialForm);
    handleToDrawing(false);
  };

  const handleToRemove = (id: string) => {
    // TODO: 사진/메모 삭제 API 호출
    console.log("사진/메모 삭제 :", id);
  };

  const handleToSave = () => {
    if (form.photoMemoId) {
      // TODo: 사진/메모 수정 API 호출
      console.log("사진/메모 수정 :", form);
    } else {
      // TODO : 사진/메모 등록 API 호출
      console.log("사진/메모 등록 :", form);
    }
  };

  console.log(form);

  return (
    <>
      <Button variant="outline" onClick={() => setIsOpen(true)}>
        <PlusIcon />
        사진/메모 추가
      </Button>

      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetContent side="right" className="z-999">
          {/* 목록 */}
          <SheetHeader>
            <SheetTitle>목록</SheetTitle>
          </SheetHeader>
          <div className="flex flex-col px-4">
            {photoMemoList.map((data) => (
              <div
                key={data.photoMemoId}
                className="flex items-center justify-between gap-5 mb-2"
              >
                <p>• {data.sj}</p>
                <div className="flex gap-2">
                  <Button onClick={() => setForm(data)}>수정</Button>
                  <Button onClick={() => handleToRemove(data.photoMemoId)}>
                    삭제
                  </Button>
                </div>
              </div>
            ))}
          </div>
          <SheetFooter className="m-0 py-0">
            <Button onClick={handleToRegister}>등록</Button>
          </SheetFooter>

          {/* 정보창 상세정보 */}
          <SheetHeader className="border-t-1">
            <SheetTitle>정보창 상세정보</SheetTitle>
            <SheetDescription>
              선택한 위치에 대한 이미지/메모를 등록하세요.
            </SheetDescription>
          </SheetHeader>
          <div className="flex flex-col gap-1 px-4">
            {textField.map(({ key, label }) => (
              <div key={key}>
                <label>{label}</label>
                <Input
                  type="text"
                  className="mt-2"
                  value={form[key] as string}
                  onChange={(e) =>
                    setForm(
                      (f) => ({ ...f, [key]: e.target.value }) as FormState,
                    )
                  }
                />
              </div>
            ))}
            <div>
              <label>이미지</label>
              <div className="flex gap-2 mt-2">
                <Input
                  type="file"
                  ref={fileInputRef}
                  style={{ display: "none" }}
                  onChange={handleFileChange}
                />
                <Input type="text" value={form.atchmnflId ?? ""} readOnly />
                <Button onClick={() => fileInputRef.current?.click()}>
                  업로드
                </Button>
              </div>
            </div>
            <div>
              <label>위치</label>
              <div className="flex gap-2 mt-2">
                <Input value={form.userMapScopeValue} readOnly />
                <Button onClick={() => handleToDrawing(true)}>재선택</Button>
              </div>
            </div>
          </div>
          <SheetFooter>
            <Button onClick={handleToSave}>저장</Button>
            <SheetClose asChild>
              <Button variant="outline" type="submit">
                닫기
              </Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </>
  );
};
