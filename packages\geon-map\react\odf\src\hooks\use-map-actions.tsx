import { BasemapInfo } from "@geon-map/core";
import { useCallback } from "react";

import { useStores } from "../contexts/map-store-context";

export function useMapActions() {
  // 🎯 안전한 스토어 사용 (격리된 스토어 우선, fallback 제공)
  const { mapStore, layerStore } = useStores();

  // ✅ Store Actions 가져오기 (Context 기반 또는 전역 스토어 사용)
  const setCenter = mapStore((state: any) => state.setCenter);
  const setZoom = mapStore((state: any) => state.setZoom);
  const panTo = mapStore((state: any) => state.panTo);
  const getTarget = mapStore((state: any) => state.getTarget);
  const layerFactory = layerStore((state: any) => state.layerFactory);

  const moveToCurrentLocation = mapStore(
    (state: any) => state.moveToCurrentLocation,
  );

  const map = mapStore((state: any) => state.map);

  // Store Actions에서 베이스맵 액션들 가져오기
  const switchBasemap = mapStore((state: any) => state.switchBasemap);
  const getCurrentBasemap = mapStore((state: any) => state.getCurrentBasemap);
  const getAvailableBasemaps = mapStore(
    (state: any) => state.getAvailableBasemaps,
  );

  // 베이스맵 변경 (Store Actions 사용)
  const setBasemap = useCallback(
    (basemapId: string) => {
      switchBasemap(basemapId);
    },
    [switchBasemap],
  );

  // 베이스맵 정보 설정
  const setBasemapInfo = useCallback(
    (basemapInfo: BasemapInfo) => {
      if (!map || !layerFactory) return;
      layerFactory.setBasemapInfo(basemapInfo);
    },
    [map, layerFactory],
  );

  // 🎯 성능 최적화: 좌표 변환 유틸리티
  const transformCoordinate = useCallback(
    (coords: [number, number], sourceProjection: string): [number, number] => {
      // 현재 스토어에서 mapInstance 가져오기
      const currentMapInstance = mapStore((state: any) => state.mapInstance);

      if (!currentMapInstance) {
        console.warn("MapController가 초기화되지 않았습니다.");
        return coords;
      }

      try {
        return currentMapInstance.transformCoordinate(coords, sourceProjection);
      } catch (error) {
        console.error("좌표 변환 실패:", error);
        return coords;
      }
    },
    [mapStore],
  );

  return {
    // Store Actions (권장 사용법)
    setCenter,
    setZoom,
    panTo,
    moveToCurrentLocation,
    getTarget,

    // 베이스맵 관련 (Store Actions 기반)
    setBasemap,
    getCurrentBasemap,
    getAvailableBasemaps,
    setBasemapInfo,

    // 유틸리티 함수
    transformCoordinate,

    // 고급 사용자용 (직접 접근) - 현재 스토어에서 최신 상태 접근
    get mapInstance() {
      return mapStore.getState().mapInstance;
    },
  };
}
