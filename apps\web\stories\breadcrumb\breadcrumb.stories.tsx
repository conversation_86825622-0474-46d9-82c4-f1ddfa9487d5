import { Breadcrumb } from "@geon-ui/react/primitives/breadcrumb";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import BreadcrumbDemoSource from "!!raw-loader!./demo";

import { BreadcrumbDemo } from "./demo";

const meta = {
  title: "Shadcn/Breadcrumb",
  component: Breadcrumb,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Breadcrumb>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: BreadcrumbDemoSource,
      },
    },
  },
  render: () => <BreadcrumbDemo />,
};
