import type { JsonResponse } from "../../utils/geonAPI";

// 각 result 아이템 타입 정의
export interface ResultItem {
  index: number;
  vertexIndex: number;
  targetPoint: string; // e.g. "POINT(127.5 37.5)"
  elevationZ: number;
  demGeometry: string; // e.g. "POINT(127.50001 37.50001)"
  cumulativeDistanceM: number;
}

// 응답 타입 정의
export type ElevationResponse = JsonResponse<ResultItem[]>;

export interface LineStringRequest {
  /** 라인스트링 WKT 문자열 */
  linestringWkt: string;
  /** 목표 좌표계 (EPSG 코드) */
  targetSrid: number;
  /** 샘플링 간격 (단위: m) */
  stepMeters: number;
}

export interface PointStringRequest {
  /** 라인스트링 WKT 문자열 */
  pointWkt: string;
  /** 목표 좌표계 (EPSG 코드) */
  targetSrid: number;
  /** 샘플링 간격 (단위: m) */
  stepMeters: number;
}

// 공지사항 리스트 조회 요청 파라미터
export interface NoticeListRequest {
  /** 내용 검색 */
  nttCn?: string;

  /** 제목 검색 */
  nttSj?: string;

  /** 페이지 번호 (1부터 시작) */
  pageIndex: number;

  /** 페이지 크기 */
  pageSize: number;

  /** 등록자 ID */
  registerId?: string;

  /** 정렬 방향 (ASC | DESC) */
  sortDirection?: "ASC" | "DESC";

  /** 정렬 필드 (예: registDt, title 등) */
  sortField?: string;
}
// 공지사항 단일 항목
export interface NoticeItem {
  /** 게시글 ID */
  nttId: string;
  /** 게시판 ID */
  bbsId: string;
  /** 카테고리 코드 */
  ctgryCode: string | null;
  /** 첨부파일 ID */
  atchmnflId: string | null;
  /** 제목 */
  nttSj: string;
  /** 내용 */
  nttCn: string;
  /** 상위 게시 여부 */
  upperExpsrAt: "Y" | "N";
  /** SMS 발송 여부 */
  smsSndngAt: "Y" | "N";
  /** 공개 여부 */
  othbcAt: "Y" | "N";
  /** 게시 시작일 */
  pstgBeginDt: string; // ISO DateTime string
  /** 조회수 */
  nttRdcnt: number;
  /** 팝업 여부 */
  popupAt: "Y" | "N";
  /** 팝업 시작일 */
  popupBeginDt: string | null;
  /** 팝업 종료일 */
  popupEndDt: string | null;
  /** 외부 링크 URL */
  linkUrl: string | null;
  /** 삭제 여부 */
  deleteAt: "Y" | "N";
  /** 삭제일 */
  deleteDt: string | null;
  /** 등록자 ID */
  registerId: string;
  /** 등록자 이름 */
  registerNm: string;
  /** 등록일시 */
  registDt: string; // ISO DateTime string
  /** 수정자 ID */
  updusrId: string | null;
  /** 수정일시 */
  updtDt: string | null;
  /** 기관 코드 */
  insttCode: string;
  /** 포털 팝업 노출 여부 */
  popupPortalExpsrAt: "Y" | "N";
  /** 기관 팝업 노출 여부 */
  popupInsttExpsrAt: "Y" | "N";
}

// 공지사항 리스트 응답
export type NoticeListResponse = JsonResponse<NoticeItem[]>;

// 공지 상세 타입
export interface AnnounceDetail {
  nttId: string;
  bbsId: string;
  ctgryCode: string | null;
  atchmnflId: string | null;
  nttSj: string;
  nttCn: string;
  upperExpsrAt: "Y" | "N";
  smsSndngAt: "Y" | "N";
  othbcAt: "Y" | "N";
  pstgBeginDt: string; // ISO "YYYY-MM-DDTHH:mm:ss[.SSS...]"
  nttRdcnt: number;
  popupAt: "Y" | "N";
  popupBeginDt: string | null;
  popupEndDt: string | null;
  linkUrl: string | null;
  deleteAt: "Y" | "N";
  deleteDt: string | null;
  registerId: string;
  registerNm: string;
  registDt: string;
  updusrId: string | null;
  updtDt: string | null;
  insttCode: string | null;
  popupPortalExpsrAt: "Y" | "N";
  popupInsttExpsrAt: "Y" | "N";
}

// 최종 응답 타입
export type AnnounceDetailResponse = JsonResponse<AnnounceDetail>;

// 공지사항 수정 요청
export interface NoticeCUDRequest {
  /** 수정할 제목 */
  nttId?: string;
  nttSj?: string;
  /** 수정할 내용 */
  nttCn?: string;
  /** 수정자 ID */
  updusrId: string;
  registerId?: string;
  popupAt?: string | boolean;
  popupBeginDt?: string | null;
  popupEndDt?: string | null;
  atchmnflId?: string | null;
}
// 공지사항 수정 결과 구조
export interface NoticeUpdateResult {
  /** 수정된 게시글 ID */
  nttId: string;
  /** 상세 메시지 */
  message: string;
}

export interface NoticeDeleteRequest {
  /** 게시글 ID */
  nttId: string;
  /** 수정자 ID */
  updusrId: string;
}

// 공지사항 수정 응답
export type NoticeCUDResponse = JsonResponse<NoticeUpdateResult>;
export interface AttachmentResponse {
  /** 첨부파일 ID */
  atchmnflId: string;

  /** 파일 순서 */
  fileOrdr: number;

  /** 첨부 파일 경로 */
  atchFlpth: string;

  /** 저장된 파일명 */
  streFileNm: string;

  /** 원본 파일명 */
  orginlFileNm: string;

  /** 파일 확장자 */
  fileExtsnNm: string;

  /** 파일 설명 / 내용 */
  fileCn: string;

  /** 파일 크기 (Byte 단위) */
  fileMg: number;
}
export interface AttachmentRequest {
  /** 첨부파일 ID */
  atchmnflId: string;
  fileOrdr?: string;
}
export type AttachmentResponseResult = JsonResponse<AttachmentResponse[]>;

export interface AttachmentUploadRequest {
  /** 첨부파일 ID */
  atchmnflId?: string;
  registerId: string;
  attachments: string;
}

export interface AttachmentReaderProps {
  attachment: AttachmentResponse[];
}

// 권한 목록 요청
export interface AuthorListRequest {
  authorType?: string;
}

// 권한
export interface Author {
  authorGroupId: string;
  authorGroupNm: string;
}

// 권한 목록 조회 응답
export type AuthorListResponse = JsonResponse<Author[]>;

// 사용자 정보와 권한정보 목록 단일 항목
export interface UserAuthorRelate {
  userNm: string;
  userId: string;
  deptNm?: string;
  mbtlnumEncpt?: string;
  administTelno?: string;
  emailaddrEncpt?: string;
  authorList?: Author[];
}

// 사용자 정보와 권한정보 목록 요청
export interface UserAuthorListRequest {
  authorType?: string;
  userNm?: string;
  userId?: string;
  deptNm?: string;
  pageIndex: number;
  pageSize: number;
}

// 사용자 정보와 권한정보 목록 응답
export type UserAuthorListResponse = JsonResponse<UserAuthorRelate[]>;

// 사용자 권한 정보 관리(등록) 요청
export interface UserAuthorInsertRequest {
  mergeUserId: string;
  userIdList: string[];
  authorIdList: string[];
}

// 사용자 권한 정보 삭제/수정 요청
export interface UserAuthorMergeRequest {
  mergeUserId: string;
  userId: string;
  authorIdList: string[];
}

/**
 * 원본 테이블명 허용 값
 */
export type SourceTable =
  | "wtl_pipe_lm"
  | "wtl_sply_ls"
  | "wtl_manh_ps"
  | "wtl_fire_ps"
  | "wtl_prga_ps"
  | "wtl_stpi_ps"
  | "wtl_flow_ps";

/**
 * 상수도 공통 인터페이스 요청 DTO (검색 조건)
 */
export interface WaterRequestDTO {
  /** 시설물 코드 (예: "SA100") */
  ftrCde?: string;

  /** 시설물 관리번호 (예: 241001) */
  ftrIdn?: number;

  /** 행정동 코드 (예: "4684036000") */
  hjdCde?: string;

  /** 도엽번호 (예: "356141180A") */
  shtNum?: string;

  /** 관리기관 코드 (예: "MNG001") */
  mngCde?: string;

  /** 설치일자 (예: "19000101") */
  istYmd?: string;

  /** 공사번호 */
  cntNum?: string;

  /** 시스템 여부 (예: "1") */
  sysChk?: string;

  /** 법정동 코드 (예: "4684036000") */
  bjdCde?: string;

  /** 원본 테이블명 목록 */
  sourceTables?: SourceTable[];

  /** 페이지 번호 (1부터 시작) */
  pageIndex: number;

  /** 페이지 크기 */
  pageSize: number;

  /** 등록자 ID */
  registerId?: string;

  /** 정렬 방향 (ASC | DESC) */
  sortDirection?: "ASC" | "DESC";

  /** 정렬 필드 (예: registDt, title 등) */
  sortField?: string;

  spatialSrid?: string;
  spatialWkt?: string;
  emdCd?: string;
}
