import { Tooltip } from "@geon-ui/react/primitives/tooltip";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import TooltipDemoSource from "!!raw-loader!./demo";

import { TooltipDemo } from "./demo";

const meta = {
  title: "Shadcn/Tooltip",
  component: Tooltip,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Tooltip>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: TooltipDemoSource,
      },
    },
  },
  render: () => <TooltipDemo />,
};
