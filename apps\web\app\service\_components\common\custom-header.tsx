import { useAppQuery } from "@geon-query/react-query";
import { But<PERSON> } from "@geon-ui/react/primitives/button";
import { BarChart3, FileSpreadsheet, Plus, Trash2 } from "lucide-react";
import { useState } from "react";

import { downloadToExcel } from "@/utils/excel-utils";
import { getTimestamp } from "@/utils/time-utils";

import { useFacilitySearch } from "../../_contexts/facility-search";
import type { FacilityDetailData } from "../../_types/facility-detail";
import {
  ServiceQueryResult,
  useServiceSearchClient,
} from "../hooks/use-service-search-client";
import { useServiceSearchSchema } from "../hooks/use-service-search-schema";
import { ServiceModal } from "./service-modal";

interface CustomHeaderProps {
  form: Record<string, unknown>;
  totalCount: number;
}

export function CustomHeader({ form, totalCount }: CustomHeaderProps) {
  const { selectedServiceId, selectedServiceTitle } = useFacilitySearch();
  const { schema } = useServiceSearchSchema(selectedServiceId);

  const [selectedCount] = useState(0); // 실제로는 선택된 시설물 수
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);

  // service client 서비스별 client API 호출 관리용 hooks
  const { client, config: serviceQueryConfig } = useServiceSearchClient(
    schema?.serviceId ?? selectedServiceId,
  );

  // React Query 연동
  const { data } = useAppQuery<ServiceQueryResult>({
    queryKey: serviceQueryConfig
      ? [serviceQueryConfig.key, schema?.serviceId ?? selectedServiceId, form]
      : ["service-search-excel", schema?.serviceId ?? selectedServiceId],
    queryFn: async () => {
      if (!serviceQueryConfig) return { rows: [], totalCount: 0, raw: null };
      return serviceQueryConfig.fetcher({
        client,
        form,
        pageSize: totalCount,
        pageNo: 1,
        resource: schema?.serviceId ?? selectedServiceId,
      });
    },
    enabled: !!totalCount,
  });

  // 테이블 컬럼 정보 조회
  const { data: columns } = useAppQuery({
    queryKey: ["magp/notice"],
    queryFn: () =>
      client.table.table({
        schema: "gcmagp",
        table: `vw_${selectedServiceId}_interface`,
      }),
    select: (res) => res.result.columns,
  });

  console.log(columns);

  const handleRegisterFacility = () => {
    setIsRegisterModalOpen(true);
  };

  const handleRegisterModalClose = () => {
    setIsRegisterModalOpen(false);
  };

  const handleRegistered = (data: FacilityDetailData) => {
    console.log("시설물 등록 완료:", data);
    alert(`${data.facilityName || "시설물"}이 등록되었습니다.`);
    // TODO: 검색 결과 새로고침
  };

  const handleExcelDownload = () => {
    if (!data) return;
    if (confirm("시설물 검색 결과를 다운로드하시겠습니까?")) {
      downloadToExcel({
        columns: columns,
        rows: data.rows,
        fileName: `${selectedServiceTitle}_${getTimestamp()}`,
      });
    }
  };

  const handleDeleteSelected = () => {
    // TODO: 선택된 시설물 삭제
    console.log("선택 삭제");
  };

  const handleStatistics = () => {
    // TODO: 통계 보기
    console.log("통계 보기");
  };

  return (
    <div className="border-b border-gray-200 py-2">
      <div className="flex items-center justify-end">
        {/* 우측: 액션 버튼들 */}
        <div className="flex items-center gap-2">
          {/* 통계 버튼 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleStatistics}
            className="text-gray-600 hover:text-gray-800"
          >
            <BarChart3 className="mr-1 h-4 w-4" />
            통계
          </Button>

          {/* 엑셀 다운로드 버튼 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleExcelDownload}
            className="text-green-600 hover:bg-green-50 hover:text-green-800"
          >
            <FileSpreadsheet className="mr-1 h-4 w-4" />
            엑셀 다운로드
          </Button>

          {/* 선택 삭제 버튼 (선택된 항목이 있을 때만) */}
          {selectedCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleDeleteSelected}
              className="text-red-600 hover:bg-red-50 hover:text-red-800"
            >
              <Trash2 className="mr-1 h-4 w-4" />
              삭제 ({selectedCount})
            </Button>
          )}

          {/* 시설물 등록 버튼 */}
          <Button
            size="sm"
            onClick={handleRegisterFacility}
            className="bg-blue-600 text-white hover:bg-blue-700"
          >
            <Plus className="mr-1 h-4 w-4" />
            등록
          </Button>
        </div>
      </div>

      {/* 시설물 등록 모달 */}
      <ServiceModal
        serviceName="road" // 현재는 road 서비스로 고정, 향후 동적으로 설정 가능
        isOpen={isRegisterModalOpen}
        onClose={handleRegisterModalClose}
        mode="register"
        facilityType="도로"
        onRegistered={handleRegistered}
      />
    </div>
  );
}
