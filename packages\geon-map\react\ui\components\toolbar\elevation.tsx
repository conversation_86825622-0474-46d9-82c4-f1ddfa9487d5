"use client";

import {
  useDraw,
  useF<PERSON>ure,
  useLayer,
  useMap,
  useProjection,
} from "@geon-map/react-odf";
import {
  BASE_URL,
  createGeonMagpClient,
  crtfckey,
  ResultItem,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { DialogContent } from "@geon-ui/react/compounds/draggable-dialog/dialog-content";
import { DialogFooter } from "@geon-ui/react/compounds/draggable-dialog/dialog-footer";
import { DialogHeader } from "@geon-ui/react/compounds/draggable-dialog/dialog-header";
import { DraggableDialog } from "@geon-ui/react/compounds/draggable-dialog/index";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  Area,
  AreaChart,
  CartesianGrid,
  ChartContainer,
  ChartTooltipContent,
  Customized,
  ReferenceDot,
  Toolt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@geon-ui/react/primitives/chart";
import { Popover, PopoverTrigger } from "@geon-ui/react/primitives/popover";
import { Dot, DownloadIcon, Minus, PrinterIcon, SlashIcon } from "lucide-react";
import * as React from "react";
import { RefObject, SetStateAction, useEffect, useRef, useState } from "react";

import { useDomCapture } from "../../hooks/use-dom-capture";
import { Button as ToolbarButton } from "./base/button";
import {
  ToolbarContent,
  ToolbarItem,
  ToolbarTrigger,
} from "./base/toolbar-item";

export type ElevationType = "lineString" | "point";

// Context for ToolbarElevation
interface ElevationContextValue {
  drawing: { type: string; wkt: string };
  setDrawing: React.Dispatch<SetStateAction<{ type: string; wkt: string }>>;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  data?: ResultItem[] | ResultItem;
}

const ElevationContext = React.createContext<ElevationContextValue | null>(null);

const useElevationContext = () => {
  const context = React.useContext(ElevationContext);
  if (!context) {
    throw new Error(
      "ToolbarElevation components must be used within ToolbarElevation",
    );
  }
  return context;
};

// Props for compound components
export interface ToolbarElevationProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export interface ToolbarElevationTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
}

export interface ToolbarElevationContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export interface ToolbarElevationToolProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 경사도 측정 모드 */
  elevationType: ElevationType;
}

// Chart Components
interface ElevationChartProps extends React.HTMLAttributes<HTMLDivElement> {
  data: ResultItem[];
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

interface ElevationResultProps extends React.HTMLAttributes<HTMLDivElement> {
  data: ResultItem;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

// 경사도 분석 팝업 공통 레이어
const ElevationDialog = ({
  isOpen,
  setIsOpen,
  layer,
  children,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  layer?: any;
  children: React.ReactNode;
}) => {
  const { clearAll } = useDraw();
  const onClose = () => {
    layer?.clearFeatures();
    clearAll();
    setIsOpen(false);
  };

  return (
    <DraggableDialog
      className="w-[800px] h-[600px]"
      defaultPosition={{ x: -500, y: -100 }}
      isOpen={isOpen}
      onClose={onClose}
    >
      <DialogHeader>
        <p>경사도 분석</p>
      </DialogHeader>
      <DialogContent>{children}</DialogContent>
      <DialogFooter />
    </DraggableDialog>
  );
};

const ElevationButtons = ({
  targetRef,
}: {
  targetRef: RefObject<HTMLDivElement | null>;
}) => {
  const { download, print } = useDomCapture(targetRef);

  return (
    <div className="flex gap-2">
      <Button variant="outline" onClick={() => download("평균경사도.png")}>
        차트 다운로드
        <DownloadIcon />
      </Button>
      <Button variant="outline" onClick={() => print()}>
        차트 인쇄하기
        <PrinterIcon />
      </Button>
    </div>
  );
};

// LineString 차트 팝업
const ElevationChart = ({ data, isOpen, setIsOpen }: ElevationChartProps) => {
  const { map, odf } = useMap();
  const { projectGeom } = useProjection();
  const f = useFeature();

  const setColor = (type: "start" | "end") => {
    const style = odf.StyleFactory.produce({
      image: {
        circle: {
          radius: 5,
          stroke: { color: `${type === "start" ? "blue" : "red"}`, width: 2 },
          fill: { color: `${type === "start" ? "blue" : "red"}` },
        },
      },
    });
    return style;
  };

  const [vertex, setVertex] = useState<ResultItem[]>([]);
  const [pointLayer, setPointLayer] = useState<any | null>(null);
  const [point, setPoint] = useState<{
    start?: ResultItem;
    end?: ResultItem;
  }>({});
  const [slope, setSlope] = useState("차트에서 시작지점을 선택해주세요.");

  const ref = useRef<HTMLDivElement | null>(null);

  const chartConfig = {
    elevation: { label: "Elevation", color: "#A4D4EA" },
    distance: { label: "Distance", color: "#000000" },
    tooltip: { label: "Tooltip", color: "#ffffff" },
  };

  const tickFormat = (value: number) => {
    const format = value !== 0 ? `${value} m` : `${value}`;
    return format;
  };

  const maxDistance = Math.max(
    ...data.map((d: ResultItem) => d.cumulativeDistanceM),
  );
  const desiredTicks = 10;
  const step = 5;
  const rawInterval = maxDistance / (desiredTicks - 1);
  const interval = Math.ceil(rawInterval / step) * step;
  const ticks: number[] = [];
  for (let i = 0; i <= maxDistance; i += interval) {
    ticks.push(Math.round(i * 100) / 100);
  }

  useEffect(() => {
    if (data) setVertex(data.filter((p: ResultItem) => p.vertexIndex));
  }, [data]);

  useEffect(() => {
    const pointLayer = odf.LayerFactory.produce("empty");
    pointLayer.setMap(map);
    setPointLayer(pointLayer);
  }, [map, odf]);

  const pickChartPoint = (e: any) => {
    if (!e || !e.activePayload) return;
    const pick = e.activePayload[0].payload;
    setPoint((prev) => {
      const mapPoint = projectGeom(f.fromWKT(pick.targetPoint), "5186");
      mapPoint.setStyle(prev.start ? setColor("end") : setColor("start"));
      pointLayer?.addFeature(mapPoint);
      if (!prev.start) {
        setSlope("차트에서 종료지점을 선택해주세요.");
        return { start: pick };
      } else if (!prev.end) {
        const slopeResult = getSlope(prev.start, pick);
        setSlope(slopeResult);
        return { ...prev, end: pick };
      } else {
        pointLayer.clearFeatures();
        const mapPoint = projectGeom(f.fromWKT(pick.targetPoint), "5186");
        mapPoint.setStyle(setColor("start"));
        pointLayer?.addFeature(mapPoint);
        return { start: pick };
      }
    });
  };

  const getSlope = (start: ResultItem, end: ResultItem) => {
    const rad = Math.atan2(
      end.elevationZ - start.elevationZ,
      end.cumulativeDistanceM - start.cumulativeDistanceM,
    );
    return ((rad * 180) / Math.PI).toFixed(2);
  };

  const ConnectingLine = ({
    startX,
    startY,
    endX,
    endY,
    xAxisMap,
    yAxisMap,
  }: {
    startX: number;
    startY: number;
    endX: number;
    endY: number;
    xAxisMap: any;
    yAxisMap: any;
  }) => {
    if (!xAxisMap || !yAxisMap) return null;
    const xAxis = Object.values(xAxisMap)[0] as any;
    const yAxis = Object.values(yAxisMap)[0] as any;
    const xScale = xAxis.scale;
    const yScale = yAxis.scale;

    const x1 = xScale(startX);
    const y1 = yScale(startY);
    const x2 = xScale(endX);
    const y2 = yScale(endY);

    const midX = (x1 + x2) / 2;
    const midY = (y1 + y2) / 2;

    return (
      <>
        <line
          x1={x1}
          y1={y1}
          x2={x2}
          y2={y2}
          stroke="#a268aaff"
          strokeWidth={1}
        />
        <text
          x={midX}
          y={midY + 30}
          textAnchor="middle"
          fontSize={16}
          fontWeight="bold"
        >
          평균 경사도 : {slope}º
        </text>
      </>
    );
  };

  return (
    <ElevationDialog isOpen={isOpen} setIsOpen={setIsOpen} layer={pointLayer}>
      <div className="flex items-center justify-between">
        <p>평균 경사도 : {slope}º</p>
        <ElevationButtons targetRef={ref} />
      </div>
      <div ref={ref} className="overflow-visible" style={{ width: "100%" }}>
        <ChartContainer
          config={chartConfig}
          className="min-h-[430px] mt-7 pr-10 pt-12 pb-10"
        >
          <AreaChart data={data} onClick={pickChartPoint} accessibilityLayer>
            <CartesianGrid stroke="#bbb" strokeDasharray="3 3" />
            <XAxis
              dataKey="cumulativeDistanceM"
              ticks={ticks}
              tickFormatter={tickFormat}
            />
            <YAxis dataKey="elevationZ" tickFormatter={tickFormat} />
            <Tooltip content={<ChartTooltipContent />} />
            <Area
              dataKey="elevationZ"
              name="고도(m)"
              stroke="var(--color-elevation)"
              fill="var(--color-elevation)"
              strokeWidth={2}
            />
            <Area
              dataKey="cumulativeDistanceM"
              name="거리(m)"
              stroke="var(--color-distance)"
              fill="var(--color-distance)"
              strokeWidth={0}
              fillOpacity={0}
              activeDot={false}
            />

            {vertex.map((v) => (
              <ReferenceDot
                key={v.cumulativeDistanceM}
                x={v.cumulativeDistanceM}
                y={v.elevationZ}
                r={4}
                fill="none"
                stroke="black"
              />
            ))}
            {point.start && (
              <ReferenceDot
                x={point.start.cumulativeDistanceM}
                y={point.start.elevationZ}
                r={4}
                fill="blue"
                stroke="none"
              />
            )}
            {point.end && (
              <ReferenceDot
                x={point.end.cumulativeDistanceM}
                y={point.end.elevationZ}
                r={4}
                fill="red"
                stroke="none"
              />
            )}
            {point.start && point.end && (
              <Customized
                component={
                  <ConnectingLine
                    startX={point.start.cumulativeDistanceM}
                    startY={point.start.elevationZ}
                    endX={point.end.cumulativeDistanceM}
                    endY={point.end.elevationZ}
                    xAxisMap
                    yAxisMap
                  />
                }
              />
            )}
          </AreaChart>
        </ChartContainer>
      </div>
    </ElevationDialog>
  );
};

// Point 결과 팝업
const ElevationResult = ({ data, isOpen, setIsOpen }: ElevationResultProps) => {
  return (
    <ElevationDialog isOpen={isOpen} setIsOpen={setIsOpen}>
      <p>좌표 : {data.targetPoint}</p>
      <p>고도 : {data.elevationZ}</p>
    </ElevationDialog>
  );
};

// Main ToolbarElevation Container (Context Provider)
export const ToolbarElevation = React.forwardRef<
  HTMLDivElement,
  ToolbarElevationProps
>(({ className, children, ...props }, ref) => {
  const [drawing, setDrawing] = useState({ type: "", wkt: "" });
  const [isOpen, setIsOpen] = useState(false);

  const client = createGeonMagpClient({ baseUrl: BASE_URL, crtfckey });
  const { data } = useAppQuery({
    queryKey: ["elevation", drawing.wkt],
    queryFn: () => {
      const options = {
        targetSrid: 5186,
        stepMeters: 5,
      };

      return drawing.type === "lineString"
        ? client.elevation.line({ linestringWkt: drawing.wkt, ...options })
        : drawing.type === "point"
          ? client.elevation.point({ pointWkt: drawing.wkt, ...options })
          : null;
    },
    enabled: !!drawing.wkt,
    select: (res) => res.result,
  });

  useEffect(() => {
    if (data) setIsOpen(true);
  }, [data]);

  const contextValue = React.useMemo(
    () => ({
      drawing,
      setDrawing,
      isOpen,
      setIsOpen,
      data,
    }),
    [drawing, setDrawing, isOpen, setIsOpen, data],
  );

  return (
    <ElevationContext.Provider value={contextValue}>
      <ToolbarItem ref={ref} className={className} {...props}>
        <Popover>
          <PopoverTrigger asChild>
            {React.Children.toArray(children).find(
              (child) =>
                React.isValidElement(child) &&
                child.type === ToolbarElevationTrigger,
            )}
          </PopoverTrigger>
          {React.Children.toArray(children).find(
            (child) =>
              React.isValidElement(child) &&
              child.type === ToolbarElevationContent,
          )}
        </Popover>

        {/* LineString 차트 */}
        {data && isOpen && drawing.type === "lineString" && (
          <ElevationChart
            data={data as ResultItem[]}
            isOpen={isOpen}
            setIsOpen={setIsOpen}
          />
        )}
        {/* Point 결과 */}
        {data && isOpen && drawing.type === "point" && (
          <ElevationResult
            data={data as ResultItem}
            isOpen={isOpen}
            setIsOpen={setIsOpen}
          />
        )}
      </ToolbarItem>
    </ElevationContext.Provider>
  );
});

ToolbarElevation.displayName = "ToolbarElevation";

// ToolbarElevationTrigger Component
export const ToolbarElevationTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarElevationTriggerProps
>(
  (
    { tooltip = "경사도 측정", size = "default", className, children, ...props },
    ref,
  ) => {
    return (
      <ToolbarTrigger
        ref={ref}
        tooltip={tooltip}
        size={size}
        className={className}
        {...props}
      >
        {children || <SlashIcon className="h-4 w-4" />}
      </ToolbarTrigger>
    );
  },
);

ToolbarElevationTrigger.displayName = "ToolbarElevationTrigger";

// ToolbarElevationContent Component
export const ToolbarElevationContent = React.forwardRef<
  HTMLDivElement,
  ToolbarElevationContentProps
>(({ className, children, ...props }, ref) => {
  return (
    <ToolbarContent
      ref={ref}
      align="center"
      sideOffset={16}
      className={cn("w-fit flex flex-col gap-3 p-4", className)}
      {...props}
    >
      {children}
    </ToolbarContent>
  );
});

ToolbarElevationContent.displayName = "ToolbarElevationContent";

// ToolbarElevationTool Component (개별 경사도 도구)
export const ToolbarElevationTool = React.forwardRef<
  HTMLButtonElement,
  ToolbarElevationToolProps
>(({ elevationType, className, children, ...props }, ref) => {
  const { setDrawing } = useElevationContext();
  const { map, odf } = useMap();
  const { startDrawing, clearAll } = useDraw();
  const { drawLayer } = useLayer();
  const f = useFeature();

  const style = odf.StyleFactory.produce({
    image: {
      circle: {
        radius: 5,
        stroke: { color: "black", width: 2 },
      },
    },
  });

  const handleToDrawing = (mode: ElevationType) => {
    const { drawend } = startDrawing(mode);
    drawend((feature) => {
      if (feature.getArea() > 100000) {
        alert("100km 이하만 측정 가능합니다.");
        clearAll();
        return;
      }
      const projection = map.getProjection?.();
      const clone = feature.clone();
      const transformedFeature = projection
        ? projection.unprojectGeom(clone, "5186")
        : clone;

      const flatCoords = feature.getGeometry().flatCoordinates;
      for (let i = 0; i < flatCoords.length; i += 2) {
        const point = f?.createPointFromCoordinates([
          flatCoords[i],
          flatCoords[i + 1],
        ]);
        point.setStyle(style);
        drawLayer?.odfLayer.addFeature(point);
      }
      setDrawing({ type: mode, wkt: transformedFeature.featureToWKT().wkt });
    });
  };

  const getToolConfig = () => {
    switch (elevationType) {
      case "lineString":
        return {
          icon: Minus,
          label: "선",
          description: "선을 그려 경사도를 측정합니다",
        };
      case "point":
        return {
          icon: Dot,
          label: "점",
          description: "점을 찍어 고도를 측정합니다",
        };
      default:
        return null;
    }
  };

  const toolConfig = getToolConfig();
  if (!toolConfig) return null;

  const Icon = toolConfig.icon;

  return (
    <ToolbarButton
      ref={ref}
      variant="ghost"
      size="sm"
      onClick={() => handleToDrawing(elevationType)}
      className={cn(
        "flex items-center justify-start gap-2 h-8 px-3 min-w-32",
        className,
      )}
      title={toolConfig.description}
      {...props}
    >
      {children || (
        <>
          <Icon className="h-4 w-4" />
          <span className="text-sm">{toolConfig.label}</span>
        </>
      )}
    </ToolbarButton>
  );
});

ToolbarElevationTool.displayName = "ToolbarElevationTool";