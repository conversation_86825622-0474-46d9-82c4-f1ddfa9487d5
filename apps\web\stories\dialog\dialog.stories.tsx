import { Dialog } from "@geon-ui/react/primitives/dialog";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import DialogDemoSource from "!!raw-loader!./demo";

import { DialogDemo } from "./demo";

const meta = {
  title: "Shadcn/Dialog",
  component: Dialog,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Dialog>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: DialogDemoSource,
      },
    },
  },
  render: () => <DialogDemo />,
};
