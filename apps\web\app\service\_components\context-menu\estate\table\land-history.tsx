"use client";

import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { createColumnHelper } from "@tanstack/react-table";
import { useFormatter, useTranslations } from "next-intl";
import React from "react";

import { Pagination, ViewTable } from "@/components/table";

export default function LandHistory({
  client,
  ...props
}: APIRequestType<EstateClient["land"]["history"]> & {
  client: EstateClient;
}) {
  // message handler
  const t = useTranslations("estate.land.history");
  const f = useFormatter();
  // date formatting
  const date = (d: string) => (d.length ? f.dateTime(new Date(d)) : "-");

  // Pagination States
  const [numOfRows, setNumOfRows] = React.useState<number>(props.numOfRows);
  const [pageNo, setPageNo] = React.useState<number>(props.pageNo);

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["land"]["history"]>
  >({
    queryKey: ["land/history", { ...props, numOfRows, pageNo }],
    queryFn: () => client.land.history({ ...props, numOfRows, pageNo }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading parcel data: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  if (!data.result.resultList?.length)
    return (
      <div className="flex size-full items-center justify-center">No Data</div>
    );

  const helper = createColumnHelper<(typeof data.result.resultList)[0]>();
  const columns = [
    helper.accessor("lndcgrCodeNm", {
      cell: (info) => info.getValue(),
      header: t("lndcgrCodeNm"),
    }),
    helper.accessor("ladMvmnPrvonshCodeNm", {
      cell: (info) => info.getValue(),
      header: t("ladMvmnPrvonshCodeNm"),
    }),
    helper.accessor("ladMvmnDe", {
      cell: (info) => date(info.getValue() as string),
      header: t("ladMvmnDe"),
    }),
    helper.accessor("ladMvmnErsrDe", {
      cell: (info) => date(info.getValue() as string),
      header: t("ladMvmnErsrDe"),
    }),
    helper.accessor("lndpclAr", {
      cell: (info) => f.number(Number(info.getValue())),
      header: t("lndpclAr"),
    }),
  ];

  return (
    <div className="flex w-full shrink-0 flex-col overflow-hidden overflow-y-auto">
      <ViewTable data={data.result.resultList} columns={columns} pinHeader />
      {data.result.pageInfo && (
        <Pagination
          type="server"
          pageInfo={data.result.pageInfo}
          onPageNoChange={setPageNo}
          onNumOfRowsChange={(newNumOfRows) => {
            setNumOfRows(newNumOfRows);
            setPageNo(1);
          }}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
