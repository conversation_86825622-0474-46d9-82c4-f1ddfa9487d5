"use client";

import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { createColumnHelper } from "@tanstack/react-table";
import { useFormatter, useTranslations } from "next-intl";
import React from "react";

import { ClientTable, SortableColumnHeader } from "@/components/table";

export default function PricePclnd({
  client,
  ...props
}: APIRequestType<EstateClient["price"]["pclnd"]> & {
  client: EstateClient;
}) {
  // message handler
  const t = useTranslations("estate.price.pclnd");
  const f = useFormatter();
  // date formatting
  const date = (d: string) => (d.length ? f.dateTime(new Date(d)) : "-");

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["price"]["pclnd"]>
  >({
    queryKey: ["price/pclnd", { ...props }],
    queryFn: () => client.price.pclnd({ ...props }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading parcel data: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  if (!data.result.resultList?.length)
    return (
      <div className="flex size-full items-center justify-center">No Data</div>
    );

  const helper = createColumnHelper<(typeof data.result.resultList)[0]>();
  const columns = [
    helper.accessor("stdrYear", {
      cell: (info) => info.getValue(),
      header: ({ column }) => (
        <SortableColumnHeader column={column} title={t("stdrYear")} />
      ),
    }),
    helper.accessor("regstrSeCodeNm", {
      cell: (info) => info.getValue(),
      header: t("regstrSeCodeNm"),
    }),
    helper.accessor("pblntfDe", {
      cell: (info) => date(info.getValue()),
      header: t("pblntfDe"),
    }),
    helper.accessor("pblntfPclnd", {
      cell: (info) => (
        <div className="text-right">
          {f.number(Number(info.getValue()), {
            style: "currency",
            currency: "KRW",
          })}
        </div>
      ),
      header: t("pblntfPclnd"),
    }),
  ];

  return (
    <div className="relative flex w-full flex-col overflow-y-auto">
      <ClientTable
        data={data.result.resultList}
        columns={columns}
        pagination
        pinHeader
      />
    </div>
  );
}
