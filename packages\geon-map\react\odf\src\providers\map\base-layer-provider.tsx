"use client";

import React, { useCallback, useEffect } from "react";

import { BaseLayer } from "../../components/layer/base-layer";
import { useControlsConfig } from "../../contexts/controls-config-context";
import { useStores } from "../../contexts/map-store-context";
import { useMap } from "../../hooks/use-map";
import type { BaseLayerInfo } from "../../types/layer-types";

/**
 * BaseLayerProvider 설정 옵션
 */
export interface BaseLayerProviderOptions {
  // 🆕 실제 레이어 렌더링용 props
  /** 배경 레이어 목록 */
  baseLayers?: BaseLayerInfo[];
  /** 오버레이 레이어 목록 */
  overlayLayers?: BaseLayerInfo[];
  /** 활성 배경 레이어 ID */
  activeBaseLayerId?: string;

  /** BaseLayer Control 초기화 옵션 (기존 호환성) */
  baseLayerOptions?: {
    baseLayers?: any;
    activeLayerId?: string;
    isLoading?: boolean;
    // 기존 호환성용 (deprecated)
    basemapList?: any;
    urls?: any;
  };
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 BaseLayerProvider (Basemap Control 설정 전용)
 *
 * Basemap Control 설정을 ControlsProvider에 전달하는 Config Provider입니다.
 * 실제 초기화는 ControlsProvider에서 수행됩니다.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <ControlsProvider>
 *     <BaseLayerProvider baseLayerOptions={{ baseLayers: [...] }}>
 *       <BasemapSwitcher />
 *     </BaseLayerProvider>
 *   </ControlsProvider>
 * </MapProvider>
 * ```
 */
export function BaseLayerProvider({
  children,
  baseLayers = [],
  overlayLayers = [],
  activeBaseLayerId,
  baseLayerOptions = {},
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<BaseLayerProviderOptions>) {
  const { updateConfig } = useControlsConfig();

  // 🔍 Map과 LayerFactory 초기화 상태 확인
  const { isLoading } = useMap();
  const { layerStore } = useStores();
  const layerFactory = layerStore((state) => state.layerFactory);

  // 🔄 zustand store에 BaseLayerInfo 등록
  useEffect(() => {
    if (baseLayers.length > 0) {
      layerStore.getState().setAvailableBaseLayers(baseLayers);
      console.log(
        `✅ [BaseLayerProvider] Registered ${baseLayers.length} base layers to store`,
      );
    }
  }, [baseLayers, layerStore]);

  useEffect(() => {
    if (overlayLayers.length > 0) {
      layerStore.getState().setAvailableOverlayLayers(overlayLayers);
      console.log(
        `✅ [BaseLayerProvider] Registered ${overlayLayers.length} overlay layers to store`,
      );
    }
  }, [overlayLayers, layerStore]);

  // 🔄 Controls Config에 BaseLayer 설정 등록 (기존 호환성)
  useEffect(() => {
    updateConfig({
      baseLayerOptions,
      autoInitialize,
      onError,
    });
  }, [baseLayerOptions, autoInitialize, onError, updateConfig]);

  // 배경지도 레이어 콜백 함수들
  const handleLayerReady = useCallback((layerId: string) => {
    console.log(`✅ Base layer ready: ${layerId}`);
  }, []);

  const handleLayerError = useCallback(
    (error: Error) => {
      console.error(`❌ Base layer error:`, error);
      onError?.(error);
    },
    [onError],
  );

  // Map이 로딩 중이거나 layerFactory가 없으면 대기
  if (isLoading || !layerFactory) {
    console.log("🔄 Waiting for map and layerFactory initialization...", {
      isLoading,
      hasLayerFactory: !!layerFactory,
    });
    return <>{children}</>;
  }

  return (
    <>
      {/* 🆕 배경지도들 렌더링 */}
      {baseLayers.map((baseLayerInfo) => (
        <BaseLayer
          key={baseLayerInfo.id}
          baseLayerInfo={baseLayerInfo}
          active={baseLayerInfo.id === activeBaseLayerId}
          onLayerReady={handleLayerReady}
          onLayerError={handleLayerError}
        />
      ))}

      {/* 🆕 오버레이들 렌더링 */}
      {overlayLayers.map((overlayLayerInfo) => (
        <BaseLayer
          key={overlayLayerInfo.id}
          baseLayerInfo={overlayLayerInfo}
          active={false} // 오버레이는 기본적으로 비활성
          onLayerReady={handleLayerReady}
          onLayerError={handleLayerError}
        />
      ))}

      {children}
    </>
  );
}
