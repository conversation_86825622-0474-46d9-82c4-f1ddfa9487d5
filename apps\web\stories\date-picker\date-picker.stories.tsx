import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import DatePickerDemoSource from "!!raw-loader!./demo";

import { DatePickerDemo } from "./demo";

const meta = {
  title: "Shadcn/DatePicker",
  component: DatePickerDemo,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof DatePickerDemo>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: DatePickerDemoSource,
      },
    },
  },
  render: () => <DatePickerDemo />,
};
