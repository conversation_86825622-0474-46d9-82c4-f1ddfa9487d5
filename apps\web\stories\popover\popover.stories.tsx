import { Popover } from "@geon-ui/react/primitives/popover";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import PopoverDemoSource from "!!raw-loader!./demo";

import { PopoverDemo } from "./demo";

const meta = {
  title: "Shadcn/Popover",
  component: Popover,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Popover>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: PopoverDemoSource,
      },
    },
  },
  render: () => <PopoverDemo />,
};
