import type { TOCNode } from "@geon-map/react-ui/types";

export const TOC_DATA: TOCNode[] = [
  {
    id: "common",
    name: "공통 레이어",
    visible: true,
    type: "group",
    expanded: true,
    children: [
      {
        id: "muan_gis:magp_hilight",
        name: "무안군 기본",
        visible: true,
        type: "layer",
      },
      {
        id: "common-administrativeBoundary",
        name: "법정경계",
        visible: false,
        expanded: false,
        type: "group",
        children: [
          {
            id: "muan_gis:tl_scco_ctprvn",
            name: "시도",
            visible: false,
            type: "layer",
          },
          {
            id: "muan_gis:tl_scco_sig",
            name: "시군구",
            visible: false,
            type: "layer",
          },
          {
            id: "muan_gis:tl_scco_emd",
            name: "읍면동",
            visible: false,
            type: "layer",
          },
          {
            id: "muan_gis:tl_scco_li",
            name: "리",
            visible: false,
            type: "layer",
          },
        ],
      },
    ],
  },
];
