import { Card } from "@geon-ui/react/primitives/card";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import CardDemoSource from "!!raw-loader!./demo";

import { CardDemo } from "./demo";

const meta = {
  title: "Shadcn/Card",
  component: Card,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: CardDemoSource,
      },
    },
  },
  render: () => <CardDemo />,
};
