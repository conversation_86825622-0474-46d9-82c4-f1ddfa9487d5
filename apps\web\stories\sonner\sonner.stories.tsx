import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import SonnerDemoSource from "!!raw-loader!./demo";

import { SonnerDemo } from "./demo";

const meta = {
  title: "Shadcn/Sonner",
  component: SonnerDemo,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof SonnerDemo>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: SonnerDemoSource,
      },
    },
  },
  render: () => <SonnerDemo />,
};
