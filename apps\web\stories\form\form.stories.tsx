import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import FormDemoSource from "!!raw-loader!./demo";

import { FormDemo } from "./demo";

const meta = {
  title: "Shadcn/Form",
  component: FormDemo,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof FormDemo>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: FormDemoSource,
      },
    },
  },
  render: () => <FormDemo />,
};
