"use client";

import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@geon-ui/react/primitives/accordion";
import { Button } from "@geon-ui/react/primitives/button";
import { Input } from "@geon-ui/react/primitives/input";
import * as React from "react";
import * as XLSX from "xlsx";

import { useStatsContext } from "../_contexts/stats";
import { Stats } from "../_type/commVo";
import StatsDataView from "./statsDataView";

/**
 * @description 통계값 설정
 */
export default function StatsValueSetting() {
  const { commVo, setCommVo, commVoClone, commVoApply } = useStatsContext();

  // 엑셀 다운로드 헤드값
  const excelHeaders: [string, string] = ["행정구역명", "통계값"];

  const fileInputRef = React.useRef<HTMLInputElement | null>(null);

  // 통계 값 단위 업데이트 핸들러
  function handleStsUnit(e: React.ChangeEvent<HTMLInputElement>) {
    const inputVal = e.target.value;
    const vo = commVoClone();
    vo.stsUnit = inputVal;
    setCommVo(vo);
  }

  // 합계 표시 여부 업데이트 핸들러
  function handleSumYn(e: React.ChangeEvent<HTMLInputElement>) {
    const inputVal = e.target.value as "Y" | "N";
    const vo = commVoClone();
    vo.sumYn = inputVal;
    setCommVo(vo);
  }

  // 엑셀 양식 다운로드 핸들러
  function handleExcelFormDownload() {
    const dataList = commVo.dataList;
    const data = dataList.map((item: Stats) => [item.codeName, item.stsVal]);

    // 워크시트 생성 (엑셀 헤더 포함)
    const worksheet = XLSX.utils.aoa_to_sheet([excelHeaders, ...data]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "통계양식");
    XLSX.writeFile(workbook, "통계양식다운로드.xlsx");
  }

  // 엑셀 파일 업로드 핸들러
  function handleExcelUpload() {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
      fileInputRef.current.click();
    }
  }

  // 파일 입력값 처리 함수 (엑셀 파일 선택 시 실행)
  function handleFileInput(e: React.ChangeEvent<HTMLInputElement>) {
    if (!e.target.files) return;
    const file = e.target.files[0];
    if (!file) return;

    // 엑셀 파일만 처리 (확장자 체크)
    if (file.name.endsWith(".xlsx")) {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (!event.target?.result) return;
        const data = new Uint8Array(event.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });
        const sheetName = workbook.SheetNames[0] || "통계값";
        const sheet = workbook.Sheets[sheetName];
        if (!sheet) return;
        const jsonData = XLSX.utils.sheet_to_json(sheet);

        const vo = commVoClone();
        const dataList = vo.dataList;
        vo.dataList = dataList.map((item: any) => {
          jsonData.forEach((row: any) => {
            if (excelHeaders?.length === 2) {
              const cell1 = row[excelHeaders[0]];
              const cell2 = row[excelHeaders[1]];
              if (item.codeName === cell1) {
                item.stsVal = cell2;
                item.subVal = 0;
              }
            }
          });
          return item;
        });

        vo.subValYn = "Y";
        vo.subPresetYn = "Y";
        vo.subPresetVal = "";
        vo.subPresetCalUnit = 1;
        vo.calFormula = "T0";
        commVoApply(vo);
      };
      reader.readAsArrayBuffer(file);
    } else {
      console.log("엑셀(.xlsx) 파일만 업로드 해주세요!");
    }
  }

  return (
    <AccordionItem value="item_step2">
      <AccordionTrigger className="bg-gray-300 px-4 py-2 text-black">
        통계값
      </AccordionTrigger>

      <AccordionContent className="space-y-6 bg-gray-50 p-4">
        <div>
          {/* 통계 값 단위 입력 */}
          <div>
            <div className="mb-2 bg-gray-200 font-semibold text-gray-700">
              ▣ 통계 값 단위
            </div>
            <Input
              type="text"
              key="stsUnit"
              value={commVo.stsUnit}
              placeholder="단위"
              onChange={handleStsUnit}
              className="w-64"
            />
          </div>

          {/* 엑셀 업로드 */}
          <div>
            <div className="mb-2 mt-2 border-gray-300 bg-gray-200 font-semibold">
              ▣ 엑셀 업로드
            </div>
            <div className="flex gap-1">
              <Button
                onClick={handleExcelFormDownload}
                className="w-30 h-8 bg-blue-500"
              >
                양식 다운로드
              </Button>
              <Button
                onClick={handleExcelUpload}
                className="w-30 h-8 bg-blue-500"
              >
                엑셀 업로드
              </Button>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileInput}
                style={{ display: "none" }}
              />
            </div>
          </div>

          {/* 합계 표시 여부 선택 */}
          <div>
            <div className="mb-2 mt-2 border-gray-300 bg-gray-200 font-semibold">
              ▣ 합계 표시
            </div>
            <div className="flex items-center gap-2">
              <Input
                type="radio"
                id="sumY"
                name="sumYn"
                value="Y"
                checked={commVo.sumYn === "Y"}
                onChange={handleSumYn}
                className="h-4 w-4 accent-blue-600"
              />
              <label
                htmlFor="sumY"
                className="flex items-center gap-2 text-sm text-gray-700"
              >
                예
              </label>

              <Input
                type="radio"
                id="sumN"
                name="sumYn"
                value="N"
                checked={commVo.sumYn === "N"}
                onChange={handleSumYn}
                className="h-4 w-4 accent-blue-600"
              />
              <label
                htmlFor="sumN"
                className="flex items-center gap-2 text-sm text-gray-700"
              >
                아니요
              </label>
            </div>
          </div>
        </div>
        <StatsDataView />
      </AccordionContent>
    </AccordionItem>
  );
}
