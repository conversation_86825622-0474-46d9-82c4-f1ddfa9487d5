import { <PERSON>ubar } from "@geon-ui/react/primitives/menubar";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import MenubarDemoSource from "!!raw-loader!./demo";

import { MenubarDemo } from "./demo";

const meta = {
  title: "Shadcn/Menubar",
  component: Menubar,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Menubar>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: MenubarDemoSource,
      },
    },
  },
  render: () => <MenubarDemo />,
};
