import { Switch } from "@geon-ui/react/primitives/switch";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import SwitchDemoSource from "!!raw-loader!./demo";

import { SwitchDemo } from "./demo";

const meta = {
  title: "Shadcn/Switch",
  component: Switch,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Switch>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: SwitchDemoSource,
      },
    },
  },
  render: () => <SwitchDemo />,
};
