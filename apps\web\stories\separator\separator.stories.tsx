import { Separator } from "@geon-ui/react/primitives/separator";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import SeparatorDemoSource from "!!raw-loader!./demo";

import { SeparatorDemo } from "./demo";

const meta = {
  title: "Shadcn/Separator",
  component: Separator,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Separator>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: SeparatorDemoSource,
      },
    },
  },
  render: () => <SeparatorDemo />,
};
