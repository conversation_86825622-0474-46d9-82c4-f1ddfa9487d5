import { fetcher, RequestContentType, ResponseContentType } from "./fetcher";

export type API_TYPE =
  | "map"
  | "addrgeo"
  | "analysis"
  | "coord"
  | "publish"
  | "smt"
  | "estate"
  | "magp"
  | "admin"
  | ""; //로컬용

export const BASE_URL = "https://city.geon.kr/api/";
export const crtfckey = "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0";

export const WMS_URL = BASE_URL + "map/api/map/wms";
export const WFS_URL = BASE_URL + "map/api/map/wfs";
export const MAP_PROXY = BASE_URL + "map/proxy";

/**
 * 기본 API Config
 */
export interface APIConfig {
  /** 요청을 보낼 기본 URL */
  baseUrl?: string;
  /** crtfckey */
  crtfckey?: string;
  timeout?: number;
}

export type ResponseCode =
  | 0 // 성공
  | 200 // 성공
  | 940 // 올바르지 않은 인증 정보
  | 999 // 내부 서버 오류
  | 960 // 처리 요청 중인 항목이 많아 요청을 처리할 수 없음
  | 980 // 필수 파라미터 누락
  | 981; // 잘못된 파라미터 요청

interface FailedResponse {
  code: Exclude<ResponseCode, 0 | 200>;
  message: string;
  result: string;
}
interface SuccessResponse<T = unknown> {
  code: 0 | 200;
  message: string;
  result: T extends readonly any[]
    ? {
        resultList?: T;
        pageInfo?: {
          numOfRows: number;
          pageNo: number;
          totalCount: string;
        };
        errorInfo?: {
          resultCode: string;
          message: string;
        };
        [key: string]: any;
      }
    : T;
}

/**
 * General API Response interface
 *
 * @template T 응답 result 타입.
 *
 @example
 * ```typescript
 * // Success with array
 * JsonResponse<User[]> // code: 0|200, result: { resultList?: User[], ... }
 * 
 * // Success with object  
 * JsonResponse<User> // code: 0|200, result: User
 * 
 * // Failed response
 * JsonResponse<User> // code: 940|999|960|980|981, result: string
 * ```
 */
export type JsonResponse<T = unknown> = FailedResponse | SuccessResponse<T>;

export interface BlobResponse {
  headers: Headers;
  blob: Blob;
  code?: number;
  message?: string;
  result?: unknown;
}

/**
 * API endpoint factory helper
 *
 * @example
 * ```typescript
 * // 기본 request 타입이 any 로 설정된 api helper 생성
 * const api = apiHelper<any>({ type, baseUrl, crtfckey });
 * ```
 *
 * @template D default 요청 파라미터 타입
 */
export function apiHelper<D extends Record<string, any>, T = any>({
  type,
  baseUrl,
  crtfckey,
}: Omit<ApiUrlParams, "endpoint">) {
  return {
    /**
     * `fetcher.get` factory 함수
     *
     * @example
     * ```typescript
     * const api = apiHelper<any>({ ... });
     * // 요청 파라미터가 { crtfckey?: string } 이고, 응답 형태는 기본(APIResponse), endpoint 주소를 "/endpoint/url"로 갖는 fetcher.get
     * api.get<{ crtfckey?: string }>("/endpoint/url");
     * ```
     * @template P 요청 파라미터 타입
     * @template R 응답 타입
     *
     * @param endpoint endpoint url
     * @returns `Promise<R>`
     */
    get:
      <P extends Record<string, any> = D, R = T>(endpoint: string) =>
      (params: P) =>
        fetcher.get<R>(apiUrl({ endpoint, type, baseUrl, crtfckey }), params),
    /**
     * `fetcher.post` factory 함수
     *
     * @example
     * ```typescript
     * const api = apiHelper<any>({ ... });
     * // 요청 Body 가 { crtfckey?: string } 이고, 응답 형태는 기본(APIResponse), endpoint 주소를 "/endpoint/url"로 갖는 fetcher.post
     * api.post<{ crtfckey?: string }>("/endpoint/url");
     * ```
     * @template P 요청 body 타입
     * @template R 응답 타입
     *
     * @param endpoint endpoint url
     * @returns `Promise<R>`
     */
    post:
      <P extends Record<string, any> = D, R = T>(
        endpoint: string,
        requestContentType: RequestContentType = "application/json",
        responseContentType: ResponseContentType = "json",
      ) =>
      (params: P) =>
        fetcher.post<R, P>(
          apiUrl({ endpoint, type, baseUrl, crtfckey }),
          params,
          requestContentType,
          responseContentType,
        ),
    /**
     * `fetcher.post` with query params factory 함수
     *
     * @example
     * ```typescript
     * const api = apiHelper<any>({ ... });
     * // 요청 Query 가 { registerId: string; userId: string }, Body 가 { menuId: string; authorGroupId: string }
     * // 응답 형태는 number, endpoint 주소를 "/admin/user/menu/author/delete"로 갖는 fetcher.post
     * api.postWithParam<{ registerId: string; userId: string }, { menuId: string; authorGroupId: string }, number>(
     *   "/admin/user/menu/author/delete"
     * );
     * ```
     * @template Q 요청 query param 타입
     * @template B 요청 body 타입
     * @template R 응답 타입
     *
     * @param endpoint endpoint url
     * @returns `(query: Q, body: B) => Promise<R>`
     */
    /**
     * `fetcher.upload` factory 함수
     *
     * @example
     * ```typescript
     * const api = apiHelper<any>({ ... });
     * api.upload("/attachments")(
     *   { atchmnflId: "AT001", registerId: "user1" }, // query params
     *   [{ field: "attachments", file: selectedFile }], // 파일
     *   { description: "공지사항 첨부파일" } // 추가 필드
     * );
     * ```
     * @template Q 쿼리 파라미터 타입
     * @template R 응답 타입
     *
     * @param endpoint endpoint url
     * @returns `(queryParams, files, extraFields?) => Promise<R>`
     */
    upload:
      <Q extends Record<string, any> = D, R = T>(endpoint: string) =>
      (
        queryParams: Q,
        files: { field: string; file: File | Blob }[],
        extraFields?: Record<string, string | number>,
        responseContentType: ResponseContentType = "json",
      ) =>
        fetcher.upload<R>(
          apiUrl({ endpoint, type, baseUrl, crtfckey }),
          queryParams,
          files,
          extraFields,
          responseContentType,
        ),
  };
}

/**
 * 요청 타입 헬퍼
 *
 * @example
 * ```typescript
 * const client: EstateClient = createEstateClient();
 * type EstateBuildingFloorRequest = APIRequestType<typeof client.building.floor>;
 * ```
 */
export type APIRequestType<T extends (...args: any) => any> = Parameters<T>[0];

/**
 * 응답 타입 헬퍼, Promise 가 resolve 된 상태의 타입 반환
 *
 * @example
 * ```typescript
 * const client: EstateClient = createEstateClient();
 * type EstateBuildingFloorResponse = APIResponseType<typeof client.building.floor>;
 * ```
 */
export type APIResponseType<T extends (...args: any) => any> = Awaited<
  ReturnType<T>
>;

interface ApiUrlParams {
  endpoint: string;
  type: API_TYPE;
  baseUrl?: string;
  crtfckey?: string;
}
/**
 * GEON API URL을 생성하는 함수
 * @param {ApiUrlParams} params - API URL 생성 파라미터
 * @returns {string} 완성된 API URL
 * @example
 * ```typescript
 * // 기본 사용
 * const url1 = apiUrl({ endpoint: "/search", type: "addrgeo" });
 * // → "https://city.geon.kr/api/addrgeo/search?crtfckey=UxizIdSqCePz93ViFt8ghZFFJuOzvUp0"
 *
 * // 커스텀 설정 사용
 * const url2 = apiUrl({
 *   endpoint: "/address/bld",
 *   type: "addrgeo",
 *   baseUrl: "https://dev.geon.kr/api/",
 *   crtfckey: "custom-key"
 * });
 * // → "https://dev.geon.kr/api/addrgeo/address/bld?crtfckey=custom-key"
 * ```
 */
export const apiUrl = ({
  endpoint,
  type,
  baseUrl = BASE_URL,
  crtfckey: apiKey = crtfckey, // 파라미터 이름을 apiKey로 변경
}: ApiUrlParams): string => `${baseUrl}${type}${endpoint}?crtfckey=${apiKey}`;
