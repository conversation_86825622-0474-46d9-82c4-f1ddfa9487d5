import { Tabs } from "@geon-ui/react/primitives/tabs";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import TabsDemoSource from "!!raw-loader!./demo";

import { TabsDemo } from "./demo";

const meta = {
  title: "Shadcn/Tabs",
  component: Tabs,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Tabs>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: TabsDemoSource,
      },
    },
  },
  render: () => <TabsDemo />,
};
