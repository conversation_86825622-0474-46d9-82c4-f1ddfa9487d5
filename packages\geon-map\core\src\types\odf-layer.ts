export interface OdfLayer {
  // Core ODF layer methods we use
  setMap(map: any): void;
  removeMap(map: any): void;
  setOpacity(opacity: number): void;
  getOpacity(): number;
  setVisible(visible: boolean): void;
  setZIndex?(zIndex: number): void;
  fit(duration?: number): void;

  // Feature operations
  addFeature(feature: any): boolean;
  addFeatures(features: any[]): boolean;
  removeFeature(feature: any): boolean;
  removeFeatureById(featureId: string | number): boolean;
  clearFeatures(): boolean;
  getFeatures(): any[];
  getFeatureById(featureId: string | number): any | null;

  // Export/import
  toKML(downloadFile?: boolean): string | null;
  toGeoJson(): any | null;
  fromKML?(
    kml: string,
    dataProjectionCode?: string,
    featureProjectionCode?: string,
  ): boolean;
  fromGeoJson?(
    geoJson: any,
    dataProjectionCode?: string,
    featureProjectionCode?: string,
  ): boolean;

  // Style
  getStyle(): any;
  setStyle(style: any): void;
  setSLD(style: any): void;

  // Misc
  getODFId(): string;
  getInitialOption(): { params: { layer: string; projection?: string } };

  // For spatial query compatibility
  getGeometryName(): string;

  clone(): OdfLayer;
}
