import { Table } from "@geon-ui/react/primitives/table";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import TableDemoSource from "!!raw-loader!./demo";

import { TableDemo } from "./demo";

const meta = {
  title: "Shadcn/Table",
  component: Table,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Table>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: TableDemoSource,
      },
    },
  },
  render: () => <TableDemo />,
};
