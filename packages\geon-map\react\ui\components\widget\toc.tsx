import { cn } from "@geon-ui/react/lib/utils";
import { But<PERSON> } from "@geon-ui/react/primitives/button";
import { ScrollArea } from "@geon-ui/react/primitives/scroll-area";
import { Slider } from "@geon-ui/react/primitives/slider";
import { ChevronDown, ChevronRight, Eye, EyeOff, Layers } from "lucide-react";
import React, { useCallback, useMemo } from "react";

// Layer 타입 정의
interface Layer {
  id: string;
  name: string;
  visible: boolean;
  opacity?: number;
  type: string;
}

// 단순한 TOC 데이터 구조
interface SimpleTOCLayer {
  id: string;
  name: string;
  visible: boolean;
  opacity: number;
  type: "layer";
}

interface SimpleTOCGroup {
  id: string;
  name: string;
  visible: boolean;
  expanded: boolean;
  type: "group";
  children: (SimpleTOCLayer | SimpleTOCGroup)[];
}

type SimpleTOCNode = SimpleTOCLayer | SimpleTOCGroup;

interface SimpleTOCProps {
  layers: Layer[];
  excludeTypes?: string[];
  onLayerToggle: (id: string, visible: boolean) => void;
  onLayerOpacityChange: (id: string, opacity: number) => void;
  onGroupToggle?: (groupId: string, expanded: boolean) => void;
  onLayerZoom?: (id: string) => void;
  className?: string;
  showHeader?: boolean;
  isLayerOpacityEnabled?: boolean;
}

// 그룹 렌더링 컴포넌트
const SimpleTOCGroupItem = React.memo<{
  group: SimpleTOCGroup;
  level: number;
  onLayerToggle: (id: string, visible: boolean) => void;
  onLayerOpacityChange: (id: string, opacity: number) => void;
  onGroupToggle: (groupId: string, expanded: boolean) => void;
  onLayerZoom?: (id: string) => void;
  isLayerOpacityEnabled?: boolean;
}>(
  ({
    group,
    level,
    onLayerToggle,
    onLayerOpacityChange,
    onGroupToggle,
    onLayerZoom,
    isLayerOpacityEnabled,
  }) => {
    const indentStyle = { paddingLeft: `${level * 16}px` };

    const handleGroupToggle = useCallback(() => {
      onGroupToggle(group.id, !group.expanded);
    }, [group.id, group.expanded, onGroupToggle]);

    const handleVisibilityToggle = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        onLayerToggle(group.id, !group.visible);
      },
      [group.id, group.visible, onLayerToggle],
    );

    return (
      <div className="select-none">
        {/* 그룹 헤더 */}
        <div
          className="flex items-center gap-2 p-2 hover:bg-gray-50 cursor-pointer"
          style={indentStyle}
          onClick={handleGroupToggle}
        >
          <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
            {group.expanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleVisibilityToggle}
            className="h-6 w-6 p-0"
          >
            {group.visible ? (
              <Eye className="h-4 w-4 text-green-600" />
            ) : (
              <EyeOff className="h-4 w-4 text-gray-400" />
            )}
          </Button>

          <Layers className="h-4 w-4 text-blue-600" />
          <span className="flex-1 text-sm font-medium">{group.name}</span>
        </div>

        {/* 자식 요소들 */}
        {group.expanded && group.children && (
          <div>
            {group.children.map((child) =>
              child.type === "group" ? (
                <SimpleTOCGroupItem
                  key={child.id}
                  group={child}
                  level={level + 1}
                  onLayerToggle={onLayerToggle}
                  onLayerOpacityChange={onLayerOpacityChange}
                  onGroupToggle={onGroupToggle}
                  onLayerZoom={onLayerZoom}
                  isLayerOpacityEnabled={isLayerOpacityEnabled}
                />
              ) : (
                <SimpleTOCLayerItem
                  key={child.id}
                  layer={child}
                  level={level + 1}
                  onLayerToggle={onLayerToggle}
                  onLayerOpacityChange={onLayerOpacityChange}
                  onLayerZoom={onLayerZoom}
                  isLayerOpacityEnabled={isLayerOpacityEnabled}
                />
              ),
            )}
          </div>
        )}
      </div>
    );
  },
);
SimpleTOCGroupItem.displayName = "SimpleTOCGroupItem";

// 레이어 렌더링 컴포넌트
const SimpleTOCLayerItem = React.memo<{
  layer: SimpleTOCLayer;
  level: number;
  onLayerToggle: (id: string, visible: boolean) => void;
  onLayerOpacityChange: (id: string, opacity: number) => void;
  onLayerZoom?: (id: string) => void;
  isLayerOpacityEnabled?: boolean;
}>(
  ({
    layer,
    level,
    onLayerToggle,
    onLayerOpacityChange,
    onLayerZoom,
    isLayerOpacityEnabled,
  }) => {
    const indentStyle = { paddingLeft: `${level * 16}px` };

    const handleVisibilityToggle = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        onLayerToggle(layer.id, !layer.visible);
      },
      [layer.id, layer.visible, onLayerToggle],
    );

    const handleLayerClick = useCallback(() => {
      onLayerZoom?.(layer.id);
    }, [layer.id, onLayerZoom]);

    const handleOpacityChange = useCallback(
      (values: number[]) => {
        const opacity = (values[0] ?? 100) / 100;
        onLayerOpacityChange(layer.id, opacity);
      },
      [layer.id, onLayerOpacityChange],
    );

    return (
      <div
        className="flex items-center gap-2 p-2 hover:bg-gray-50 cursor-pointer"
        style={indentStyle}
        onClick={handleLayerClick}
      >
        {/* 들여쓰기 공간 */}
        <div className="w-4" />

        <Button
          variant="ghost"
          size="sm"
          onClick={handleVisibilityToggle}
          className="h-6 w-6 p-0"
        >
          {layer.visible ? (
            <Eye className="h-4 w-4 text-green-600" />
          ) : (
            <EyeOff className="h-4 w-4 text-gray-400" />
          )}
        </Button>

        <span className="flex-1 text-sm truncate">{layer.name}</span>

        {/* Opacity Slider */}
        {isLayerOpacityEnabled && (
          <div className="w-20" onClick={(e) => e.stopPropagation()}>
            <Slider
              value={[layer.opacity * 100]}
              onValueChange={handleOpacityChange}
              max={100}
              step={1}
              className="w-full"
            />
          </div>
        )}
      </div>
    );
  },
);
SimpleTOCLayerItem.displayName = "SimpleTOCLayerItem";

// 메인 SimpleTOC 컴포넌트
export const SimpleTOC = React.forwardRef<HTMLDivElement, SimpleTOCProps>(
  (
    {
      layers,
      excludeTypes = ["draw", "measure", "empty", "base"],
      onLayerToggle,
      onLayerOpacityChange,
      onGroupToggle,
      onLayerZoom,
      className,
      showHeader = true,
      isLayerOpacityEnabled = false,
      ...props
    },
    ref,
  ) => {
    // layers를 SimpleTOCNode 형태로 변환 (필터링 포함)
    const data = useMemo((): SimpleTOCNode[] => {
      return layers
        .filter((layer) => !excludeTypes.includes(layer.type))
        .map((layer) => ({
          id: layer.id,
          name: layer.name,
          visible: layer.visible,
          opacity: layer.opacity ?? 1,
          type: "layer" as const,
        }));
    }, [layers, excludeTypes]);

    // 더미 그룹 토글 핸들러
    const handleGroupToggle = useCallback(
      (groupId: string, expanded: boolean) => {
        onGroupToggle?.(groupId, expanded);
      },
      [onGroupToggle],
    );
    return (
      <div
        ref={ref}
        className={cn(
          "bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden flex flex-col",
          className,
        )}
        {...props}
      >
        {/* Header */}
        {showHeader && (
          <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50 flex-shrink-0">
            <h3 className="text-sm font-medium text-gray-900">레이어 목록</h3>
            <Button variant="ghost" size="sm">
              <Layers className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Body with ScrollArea */}
        <div className="flex-1 min-h-0">
          <ScrollArea className="h-[400px] w-full">
            <div className="p-2 space-y-1">
              {data.length === 0 ? (
                <div className="text-center text-gray-500 text-sm py-4">
                  표시할 레이어가 없습니다
                </div>
              ) : (
                data.map((node) =>
                  node.type === "group" ? (
                    <SimpleTOCGroupItem
                      key={node.id}
                      group={node}
                      level={0}
                      onLayerToggle={onLayerToggle}
                      onLayerOpacityChange={onLayerOpacityChange}
                      onGroupToggle={handleGroupToggle}
                      onLayerZoom={onLayerZoom}
                      isLayerOpacityEnabled={isLayerOpacityEnabled}
                    />
                  ) : (
                    <SimpleTOCLayerItem
                      key={node.id}
                      layer={node}
                      level={0}
                      onLayerToggle={onLayerToggle}
                      onLayerOpacityChange={onLayerOpacityChange}
                      onLayerZoom={onLayerZoom}
                      isLayerOpacityEnabled={isLayerOpacityEnabled}
                    />
                  ),
                )
              )}
            </div>
          </ScrollArea>
        </div>
      </div>
    );
  },
);
SimpleTOC.displayName = "SimpleTOC";

export type { SimpleTOCGroup, SimpleTOCLayer, SimpleTOCNode, SimpleTOCProps };
