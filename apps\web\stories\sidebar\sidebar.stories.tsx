import { Sidebar } from "@geon-ui/react/primitives/sidebar";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import SidebarDemoSource from "!!raw-loader!./demo";

import { SidebarDemo } from "./demo";

const meta = {
  title: "Shadcn/Sidebar",
  component: Sidebar,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "fullscreen",
  },
} satisfies Meta<typeof Sidebar>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: SidebarDemoSource,
      },
    },
  },
  render: () => <SidebarDemo />,
};
