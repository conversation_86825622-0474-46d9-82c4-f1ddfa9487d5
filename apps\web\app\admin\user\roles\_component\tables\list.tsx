"use client";

import {
  type APIRequestType,
  type APIResponseType,
  createGeonMagpClient,
  type MagpClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { createColumnHelper } from "@tanstack/react-table";
import { useSearchParams } from "next/navigation";
import React from "react";

import Pagination from "@/components/table/pagination";
import ViewTable from "@/components/table/view";

import Create from "./create";
import Delete from "./delete";

//TODO 아이디 store에서 호출
const tempCurrentUserId = "admin";
export default function List({
  pageIndex,
  onPageNoChange,
  ...props
}: APIRequestType<MagpClient["author"]["list"]> & {
  onPageNoChange: (page: number) => void;
}) {
  const client = createGeonMagpClient();
  const searchParams = useSearchParams();

  const initialSize = Number(searchParams.get("size") ?? 10);

  const [numOfRows, setNumOfRows] = React.useState(initialSize);

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<MagpClient["author"]["list"]>
  >({
    queryKey: [
      "/magp/admin/author",
      { ...props, pageSize: numOfRows, pageIndex: pageIndex },
    ],
    queryFn: () =>
      client.author.list({
        ...(props as any),
        pageSize: numOfRows,
        pageIndex,
      }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading notices: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  const helper = createColumnHelper<(typeof data.result.resultList)[0]>();
  const columns = [
    helper.accessor("userNm", {
      cell: (info) => info.getValue(),
      header: "이름",
    }),
    helper.accessor("deptNm", {
      cell: (info) => (info.getValue() ? info.getValue() : "-"),
      header: "부서",
    }),
    helper.accessor("userId", {
      cell: (info) => info.getValue(),
      header: "아이디",
    }),
    helper.accessor("mbtlnumEncpt", {
      cell: (info) => (info.getValue() ? info.getValue() : "-"),
      header: "휴대전화 번호",
    }),
    helper.accessor("administTelno", {
      cell: (info) => (info.getValue() ? info.getValue() : "-"),
      header: "행정전화 번호",
    }),
    helper.accessor("emailaddrEncpt", {
      cell: (info) => (info.getValue() ? info.getValue() : "-"),
      header: "이메일",
    }),
    helper.display({
      id: "addAuthor",
      header: "권한추가",
      cell: (info) => {
        const queryKey = [
          "magp/admin/author",
          { ...props, pageSize: numOfRows, pageIndex: pageIndex },
        ];

        return (
          <Create
            mergeUserId={tempCurrentUserId}
            userIdList={[info.row.original.userId]}
            listQueryKey={queryKey}
          />
        );
      },
    }),
    helper.accessor("authorList", {
      cell: (info) => {
        const authorList = info.getValue();
        if (!Array.isArray(authorList) || authorList.length === 0) return "-";

        const queryKey = [
          "magp/admin/author",
          { ...props, pageSize: numOfRows, pageIndex: pageIndex },
        ];

        return (
          <div className="flex flex-col gap-1">
            {authorList.map((author) => (
              <div
                key={author.authorGroupId}
                className="flex items-center justify-between"
              >
                <span className="truncate">{author.authorGroupNm}</span>
                <Delete
                  mergeUserId={tempCurrentUserId}
                  userId={info.row.original.userId}
                  authorIdList={[author.authorGroupId]}
                  listQueryKey={queryKey}
                />
              </div>
            ))}
          </div>
        );
      },
      header: "권한목록",
    }),
  ];

  return (
    <div className="flex w-full max-w-[1500px] flex-col overflow-hidden overflow-y-auto">
      <ViewTable data={data.result["resultList"]} columns={columns} pinHeader />
      {typeof data?.result !== "string" && data?.result.pageInfo && (
        <Pagination
          type="server"
          pageInfo={data.result.pageInfo}
          onPageNoChange={onPageNoChange}
          onNumOfRowsChange={(newNumOfRows) => {
            setNumOfRows(newNumOfRows);
            onPageNoChange(1);
          }}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
