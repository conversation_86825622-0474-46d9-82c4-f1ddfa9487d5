import { DropdownMenu } from "@geon-ui/react/primitives/dropdown-menu";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import DropdownMenuDemoSource from "!!raw-loader!./demo";

import { DropdownMenuDemo } from "./demo";

const meta = {
  title: "Shadcn/DropdownMenu",
  component: DropdownMenu,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof DropdownMenu>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: DropdownMenuDemoSource,
      },
    },
  },
  render: () => <DropdownMenuDemo />,
};
