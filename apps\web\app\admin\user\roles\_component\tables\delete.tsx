"use client";

import { But<PERSON> } from "@geon-map/react-ui/components";
import {
  createGeonMagpClient,
  UserAuthorMergeRequest,
} from "@geon-query/model";
import { useAppMutation, useAppQueryClient } from "@geon-query/react-query";
import React from "react";

interface DeleteProps extends UserAuthorMergeRequest {
  listQueryKey: any;
}
export default function Delete({
  mergeUserId,
  userId,
  authorIdList,
  listQueryKey: queryKey,
}: DeleteProps) {
  const client = createGeonMagpClient();
  const qc = useAppQueryClient();

  const userAuthorRelateDeleteMutation = useAppMutation({
    mutationFn: async () => {
      return client.author.delete({
        mergeUserId,
        userId,
        authorIdList,
      });
    },
    onSuccess: (data) => {
      if (data?.result !== 0) {
        alert(`${userId}의 권한을 삭제했습니다.`);
        qc.invalidateQueries(queryKey);
      } else {
        alert(`${userId}에게 권한이 존재하지 않습니다. 관리자에게 문의하세요.`);
      }
    },
    onError: (err: any) => {
      console.error(err);
    },
  });

  return (
    <Button
      onClick={() => {
        userAuthorRelateDeleteMutation.mutate();
      }}
      className="rounded-md bg-red-500 text-xs text-white hover:bg-red-600 hover:text-white"
    >
      제거
    </Button>
  );
}
