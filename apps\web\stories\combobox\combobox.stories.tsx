import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import ComboboxDemoSource from "!!raw-loader!./demo";

import { ComboboxDemo } from "./demo";

const meta = {
  title: "Shadcn/Combobox",
  component: ComboboxDemo,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ComboboxDemo>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: ComboboxDemoSource,
      },
    },
  },
  render: () => <ComboboxDemo />,
};
