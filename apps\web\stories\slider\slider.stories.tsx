import { Slider } from "@geon-ui/react/primitives/slider";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import SliderDemoSource from "!!raw-loader!./demo";

import { SliderDemo } from "./demo";

const meta = {
  title: "Shadcn/Slider",
  component: Slider,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Slider>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: SliderDemoSource,
      },
    },
  },
  render: () => <SliderDemo />,
};
