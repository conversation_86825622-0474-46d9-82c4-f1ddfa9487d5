import { Select } from "@geon-ui/react/primitives/select";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import SelectDemoSource from "!!raw-loader!./demo";

import { SelectDemo } from "./demo";

const meta = {
  title: "Shadcn/Select",
  component: Select,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Select>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: SelectDemoSource,
      },
    },
  },
  render: () => <SelectDemo />,
};
