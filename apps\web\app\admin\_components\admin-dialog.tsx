"use client";

import { But<PERSON> } from "@geon-ui/react/primitives/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@geon-ui/react/primitives/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@geon-ui/react/primitives/table";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ReactNode, useMemo } from "react";

export type InfoRow = { label: string; value: ReactNode };

type AdminDialogProps = {
  title?: string;
  rows: InfoRow[];
  trigger?: ReactNode;
  onSubmit?: () => void;
  buttonTitle?: string;
  open?: boolean;
  defaultOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
};

const columnHelper = createColumnHelper<InfoRow>();

export function AdminDialog({
  title,
  rows,
  trigger = <Button>열기</Button>,
  buttonTitle,
  open,
  defaultOpen,
  onOpenChange,
}: AdminDialogProps) {
  const columns = useMemo(
    () => [
      columnHelper.accessor("label", {
        header: () => null,
        cell: (info) => (
          <div className="whitespace-nowrap bg-gray-100 px-4 py-2 text-left font-medium">
            {info.getValue()}
          </div>
        ),
      }),
      columnHelper.accessor("value", {
        header: () => null,
        cell: (info) => <div className="px-4 py-2">{info.getValue()}</div>,
      }),
    ],
    [],
  );

  const table = useReactTable({
    data: rows,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const handleSubmit = () => {};

  return (
    <Dialog open={open} defaultOpen={defaultOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="overflow-hidden p-2">
        <DialogHeader className="px-6 pb-3 pt-5">
          <DialogTitle className="text-center text-base font-semibold">
            {title}
          </DialogTitle>
        </DialogHeader>
        <Table className="table-auto">
          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                className="hover:bg-muted/30 even:bg-muted/10 border-b last:border-0"
              >
                {row.getVisibleCells().map((cell, idx) =>
                  idx === 0 ? (
                    <TableHead
                      key={cell.id}
                      scope="row"
                      className="bg-card sticky left-0 z-[1] px-3 py-2 text-sm font-semibold"
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableHead>
                  ) : (
                    <TableCell
                      key={cell.id}
                      className="px-3 py-2 align-top text-sm"
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ),
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <div className="flex items-center justify-end gap-2 border-t px-6 py-4">
          <Button type="button" variant="secondary" onClick={handleSubmit}>
            {buttonTitle}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
