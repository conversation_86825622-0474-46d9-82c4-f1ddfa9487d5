import * as XLSX from "xlsx";

interface TypeProps {
  columns: Record<string, unknown>[];
  columnId?: string; // header 키값
  rows: Record<string, unknown>[];
  columnName?: string; // body 키값
  fileName: string;
}

// 엑셀 다운로드
export function downloadToExcel(type: TypeProps) {
  const {
    columns,
    columnId = "columnComment",
    rows,
    columnName = "columnName",
    fileName,
  } = type;

  const toCamelCase = (snake: string) => {
    return snake.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  };

  const header = columns.map((col) => col[columnId]);
  const body = rows.map((row) =>
    columns.map((col) => row[toCamelCase(col[columnName] as string)] ?? ""),
  );

  const worksheet = XLSX.utils.aoa_to_sheet([header, ...body]);
  const workbook = XLSX.utils.book_new();

  XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/octet-stream" });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  // 파일 이름
  a.download = `${fileName}.xlsx`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
}
