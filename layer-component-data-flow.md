# Layer 선언적 컴포넌트 데이터 흐름 비교

## 개요

이 문서는 layer 선언적 컴포넌트 사용 방식으로 변경하는 작업의 데이터 흐름을 전후 비교하여 설명합니다.

- **기존 방식**: TOC 위젯에서 useLayer Hook을 직접 호출하여 데이터를 핸들링

- **새로운 방식**: 상위 부모 컴포넌트에서 Layer props를 관리하고, Layer 컴포넌트가 useLayer Hook을 호출

## 기존 방식 (Legacy)

```mermaid

graph TB

    subgraph "❌ 기존 방식 (Legacy)"

        direction TB

  

        subgraph "TOC Widget 직접 Hook 사용"

            A1[TOCWidgetService]

            A2[TOCWidget]

            A3[UseLayerTOCWidget]

            A4[useLayer Hook 직접 호출]

        end

  

        subgraph "Layer Management"

            B1[각 Widget에서 개별적으로<br/>useLayer Hook 호출]

            B2[Layer Store 직접 접근]

            B3[setVisible, setOpacity<br/>직접 호출]

        end

  

        subgraph "데이터 흐름"

            C1[TOC Widget] --> C2[useLayer Hook]

            C2 --> C3[Layer Store]

            C3 --> C4[ODF Layer]

            C4 --> C5[Map Rendering]

        end

  

        A1 --> A2

        A2 --> A3

        A3 --> A4

        A4 --> B1

        B1 --> B2

        B2 --> B3

        B3 --> C1

    end

```

### 기존 방식의 문제점

1. **복잡한 상태 관리**

   - TOC Widget이 직접 Layer를 조작

   - 여러 곳에서 useLayer Hook 호출

   - 상태 동기화 문제 발생 가능

2. **분산된 책임**

   - 각 위젯이 개별적으로 레이어 상태를 관리

   - 예측하기 어려운 데이터 흐름

3. **유지보수 어려움**

   - 상태 변경이 어디서 일어나는지 추적하기 어려움

   - 디버깅 복잡성 증가

## 새로운 방식 (Declarative Component)

```mermaid

graph TB

    subgraph "✅ 새로운 방식 (Declarative Component)"

        direction TB

  

        subgraph "Parent Component State Management"

            D1[ServiceMap<br/>상위 컴포넌트]

            D2[Layer Props State<br/>visible, opacity, etc.]

            D3[Layer State Handlers<br/>onVisibleChange, onOpacityChange]

        end

  

        subgraph "Layer Components"

            E1[ServiceLayerManager]

            E2[Layer 컴포넌트들<br/>선언적 렌더링]

            E3[useLayer Hook<br/>Layer 컴포넌트 내부에서만 호출]

        end

  

        subgraph "TOC Widget - Read Only"

            F1[TOCWidget]

            F2[TOC Store 구독<br/>읽기 전용]

            F3[상위 컴포넌트 Handler 호출<br/>상태 변경 요청]

        end

  

        subgraph "Data Flow"

            G1[Props 변경] --> G2[Layer Component]

            G2 --> G3[useLayer Hook]

            G3 --> G4[Layer Store Update]

            G4 --> G5[ODF Layer Update]

            G5 --> G6[Map Rendering]

        end

  

        D1 --> D2

        D2 --> D3

        D1 --> E1

        E1 --> E2

        E2 --> E3

  

        F1 --> F2

        F2 --> F3

        F3 --> D3

        D3 --> G1

    end

```

### 새로운 방식의 장점

1. **단일 진실 공급원 (Single Source of Truth)**

   - 상위 컴포넌트에서만 레이어 props 관리

   - 일관된 상태 관리

2. **선언적 렌더링**

   - Layer 컴포넌트가 props 변경에 따라 자동 업데이트

   - React의 선언적 패러다임과 일치

3. **예측 가능한 데이터 흐름**

   - Props → Layer Component → useLayer Hook → Store → ODF Layer

   - 명확한 데이터 흐름 경로

4. **TOC Widget 단순화**

   - 읽기 전용으로 변경

   - 상태 변경은 상위 핸들러만 호출

## 데이터 흐름 상세 비교

### 🔄 전체 데이터 흐름 비교 (한눈에 보기)

```mermaid

flowchart LR

    subgraph COL1["❌ 기존 방식 - 분산된 상태 관리"]

        direction TB

  

        subgraph TOC1["TOC 위젯 영역"]

            O1[👤 사용자 액션<br/>레이어 visible 토글]

            O2[🎛️ TOC Widget]

            O3[🪝 useLayer Hook<br/>직접 호출]

        end

  

        subgraph LAYER1["레이어 관리 영역"]

            O4[📦 Layer Store<br/>직접 접근/수정]

            O5[🗺️ ODF Layer<br/>직접 업데이트]

            O6[🖼️ Map Rendering]

        end

  

        O1 --> O2

        O2 --> O3

        O3 --> O4

        O4 --> O5

        O5 --> O6

  

        style O1 fill:#ffebee

        style O3 fill:#ffcdd2

        style O4 fill:#ef9a9a

    end

  

    subgraph ARROW["🔄"]

        ARR[마이그레이션]

        style ARR fill:#fff2cc

    end

  

    subgraph COL2["✅ 새로운 방식 - 중앙 집중식 상태 관리"]

        direction TB

  

        subgraph TOC2["TOC 위젯 영역"]

            N1[👤 사용자 액션<br/>레이어 visible 토글]

            N2[🎛️ TOC Widget<br/>읽기 전용]

            N3[📞 Handler 호출<br/>상태 변경 요청]

        end

  

        subgraph PARENT["상위 컴포넌트 영역"]

            N4[🏠 ServiceMap<br/>중앙 상태 관리]

            N5[📊 Layer Props State<br/>visible, opacity]

        end

  

        subgraph LAYER2["Layer 컴포넌트 영역"]

            N6[⚛️ Layer Component<br/>Props 기반 렌더링]

            N7[🪝 useLayer Hook<br/>내부에서만 호출]

            N8[📦 Layer Store<br/>안전한 업데이트]

            N9[🗺️ ODF Layer<br/>예측 가능한 업데이트]

            N10[🖼️ Map Rendering]

        end

  

        N1 --> N2

        N2 --> N3

        N3 --> N4

        N4 --> N5

        N5 --> N6

        N6 --> N7

        N7 --> N8

        N8 --> N9

        N9 --> N10

  

        style N1 fill:#e8f5e8

        style N4 fill:#c8e6c9

        style N6 fill:#a5d6a7

        style N7 fill:#81c784

    end

  

    COL1 --> ARROW

    ARROW --> COL2

```

### 📊 단계별 상세 비교

| 단계 | ❌ 기존 방식 | ✅ 새로운 방식 | 🎯 개선점 |

|------|-------------|---------------|----------|

| **1. 사용자 액션** | TOC에서 레이어 토글 | TOC에서 레이어 토글 | 동일 |

| **2. 이벤트 처리** | TOC가 직접 useLayer Hook 호출 | TOC가 부모 Handler 호출 | 책임 분리 |

| **3. 상태 관리** | 각 위젯에서 개별 관리 | 상위 컴포넌트에서 중앙 관리 | 일관성 확보 |

| **4. 레이어 업데이트** | useLayer Hook이 직접 실행 | Props 변경으로 자동 업데이트 | 선언적 방식 |

| **5. Store 업데이트** | 여러 곳에서 직접 접근 | Layer Component를 통해서만 | 안전성 향상 |

| **6. 결과 반영** | 즉시 반영 (동기화 문제 가능) | 예측 가능한 흐름으로 반영 | 안정성 향상 |

### 🔍 핵심 차이점 분석

```mermaid

graph LR

    subgraph PROBLEM["❌ 기존 방식의 문제점"]

        P1[🕷️ 복잡한 의존성<br/>TOC ↔ useLayer ↔ Store]

        P2[🔄 상태 동기화 위험<br/>여러 곳에서 동시 수정]

        P3[🐛 디버깅 어려움<br/>상태 변경 경로 추적 곤란]

        P4[🧩 컴포넌트 결합도 높음<br/>TOC가 Layer 로직 직접 처리]

    end

  

    subgraph SOLUTION["✅ 새로운 방식의 해결책"]

        S1[🎯 단순한 구조<br/>TOC → Parent → Layer]

        S2[🛡️ 안전한 상태 관리<br/>단일 진실 공급원]

        S3[🔬 명확한 추적<br/>Props 기반 데이터 흐름]

        S4[🔧 낮은 결합도<br/>각 컴포넌트 역할 분리]

    end

  

    P1 -.->|"해결"| S1

    P2 -.->|"해결"| S2

    P3 -.->|"해결"| S3

    P4 -.->|"해결"| S4

```

### 💡 올바른 패턴들

#### 기존 방식

```typescript

// ❌ 문제: TOC Widget에서 직접 useLayer Hook 사용

function TOCWidget() {

  const { setVisible, setOpacity } = useLayer(); // 직접 호출

  

  const handleVisibilityToggle = (layerId: string, visible: boolean) => {

    setVisible(layerId, visible); // 직접 상태 변경

  };

  

  const handleOpacityChange = (layerId: string, opacity: number) => {

    setOpacity(layerId, opacity); // 직접 상태 변경

  };

}

```

#### 완전한 Props 기반 제어  - 현재 Layer 컴포넌트 활용

```typescript

// === 새로운 방식: 현재 Layer 컴포넌트를 활용한 Props 기반 제어 ===

  

// ServiceMap - 모든 layer 상태를 중앙 관리

function ServiceMap() {

  const { splitMode, setSplitMode } = useSplitMode();

  const { selectedServiceId, appliedCQLFilter } = useFacilitySearch();

  

  // 🎯 Layer 상태 중앙 관리

  const [layerStates, setLayerStates] = useState<Record<string, LayerState>>({});

  

  // Layer 상태 변경 핸들러들

  const handleLayerVisibilityChange = useCallback((layerId: string, visible: boolean) => {

    setLayerStates(prev => ({

      ...prev,

      [layerId]: { ...prev[layerId], visible }

    }));

  }, []);

  

  const handleLayerOpacityChange = useCallback((layerId: string, opacity: number) => {

    setLayerStates(prev => ({

      ...prev,

      [layerId]: { ...prev[layerId], opacity }

    }));

  }, []);

  

  const handleLayerZIndexChange = useCallback((layerId: string, zIndex: number) => {

    setLayerStates(prev => ({

      ...prev,

      [layerId]: { ...prev[layerId], zIndex }

    }));

  }, []);

  

  return (

    <MapContainer className="relative h-full w-full" splitMode={splitMode}>

      {/* ✅ Props 기반으로 Layer 상태 전달 */}

      <ServiceLayerManager

        layerStates={layerStates}

        setLayerStates={setLayerStates}

        selectedServiceId={selectedServiceId}

        appliedCQLFilter={appliedCQLFilter}

      />

  

      {/* ✅ 핸들러들을 TOC Widget에 전달 */}

      <TOCWidgetService

        onLayerVisibilityChange={handleLayerVisibilityChange}

        onLayerOpacityChange={handleLayerOpacityChange}

        onLayerZIndexChange={handleLayerZIndexChange}

      />

  

      {/* 기존 툴바들은 그대로 유지 */}

      <RegionSelector>...</RegionSelector>

      <AddressSearch>...</AddressSearch>

      <ToolbarContainer>...</ToolbarContainer>

    </MapContainer>

  );

}

  

// ServiceLayerManager - TOC 데이터 로드 + Layer 렌더링

function ServiceLayerManager({

  layerStates,

  setLayerStates,

  selectedServiceId,

  appliedCQLFilter

}) {

  const [layerNodes, setLayerNodes] = useState<TOCNode[]>([]);

  

  // TOC 데이터 로드 (기존 로직 유지)

  useEffect(() => {

    const loadTOCData = async () => {

      // ... 기존 TOC 로딩 로직 ...

      const tocData = await loadTOCDataForService(selectedServiceId);

      const extractedLayers = extractLayersFromTOC(tocData);

  

      setLayerNodes(extractedLayers);

  

      // 🎯 초기 상태 설정 (TOC 데이터 기반)

      const initialStates = extractedLayers.reduce((acc, node) => {

        acc[node.id] = {

          visible: node.visible ?? true,

          opacity: node.opacity ?? 1,

          zIndex: node.zIndex ?? 0

        };

        return acc;

      }, {} as Record<string, LayerState>);

  

      setLayerStates(initialStates);

    };

  

    if (selectedServiceId) {

      loadTOCData();

    }

  }, [selectedServiceId, setLayerStates]);

  

  // 🚀 Layer 컴포넌트들 렌더링 (Props 기반)

  return (

    <>

      {layerNodes.map((layerNode) => {

        const layerState = layerStates[layerNode.id] || {

          visible: layerNode.visible ?? true,

          opacity: layerNode.opacity ?? 1,

          zIndex: layerNode.zIndex ?? 0

        };

  

        return (

          <Layer

            key={layerNode.id}

            id={layerNode.id}

            config={{

              type: "geoserver",

              server: { url: WMS_URL },

              layer: layerNode.id,

              service: "wms",

              method: "post",

            }}

            // ✅ Props로 상태 전달 (Layer 컴포넌트가 useEffect로 반응)

            visible={layerState.visible}

            opacity={layerState.opacity}

            zIndex={layerState.zIndex}

            cqlFilter={appliedCQLFilter}

            onLayerReady={(layerId) =>

              console.log(`Layer ready: ${layerNode.id} -> ${layerId}`)

            }

            onLayerError={(error) =>

              console.error(`Layer error: ${layerNode.id}`, error)

            }

          />

        );

      })}

    </>

  );

}

  

// TOCWidgetService - 핸들러들을 받아서 전달

function TOCWidgetService({

  onLayerVisibilityChange,

  onLayerOpacityChange,

  onLayerZIndexChange

}) {

  const [tocVisible, setTOCVisible] = useState(false);

  

  return (

    <>

      <Tooltip>

        <TooltipTrigger asChild>

          <Button onClick={() => setTOCVisible(!tocVisible)}>

            <Layers className="h-4 w-4" />

          </Button>

        </TooltipTrigger>

        <TooltipContent>레이어 목록</TooltipContent>

      </Tooltip>

  

      {tocVisible && (

        <TOCWidget

          // ✅ 핸들러들을 props로 전달

          onLayerVisibilityChange={onLayerVisibilityChange}

          onLayerOpacityChange={onLayerOpacityChange}

          onLayerZIndexChange={onLayerZIndexChange}

          excludeLayerTypes={["draw", "measure", "clear"]}

        />

      )}

    </>

  );

}

  

// TOCWidget - UseLayerTOCWidget에 핸들러 전달

function TOCWidget({

  onLayerVisibilityChange,

  onLayerOpacityChange,

  onLayerZIndexChange,

  excludeLayerTypes = ["draw", "measure", "clear"],

  className = "absolute left-5 top-20 flex max-h-[700px] min-h-[300px] w-[400px] flex-col"

}) {

  return (

    <UseLayerTOCWidget

      className={className}

      excludeLayerTypes={excludeLayerTypes}

      // ✅ 핸들러들을 전달

      onLayerVisibilityChange={onLayerVisibilityChange}

      onLayerOpacityChange={onLayerOpacityChange}

      onLayerZIndexChange={onLayerZIndexChange}

    />

  );

}

  

// UseLayerTOCWidget - useLayer Hook에서 직접 변경하지 않고 핸들러 호출

function UseLayerTOCWidget({

  onLayerVisibilityChange,

  onLayerOpacityChange,

  onLayerZIndexChange,

  excludeLayerTypes,

  className

}) {

  // ✅ 읽기 전용으로만 사용

  const { layers } = useLayer();

  

  // 🚀 필터링된 TOC 노드 생성 (기존 로직 유지)

  const tocNodes = useMemo(() => {

    return layers

      .filter(layer => !excludeLayerTypes?.includes(layer.type))

      .map(layer => ({

        id: layer.id,

        type: "layer" as const,

        name: layer.name,

        visible: layer.visible,

        opacity: layer.opacity,

        layerId: layer.id,

      }));

  }, [layers, excludeLayerTypes]);

  

  // ❌ 제거: const { setVisible, setOpacity } = useLayer();

  

  // ✅ 핸들러 호출로 변경

  const handleToggleVisibility = useCallback((id: string, visible: boolean) => {

    console.log(`[UseLayerTOCWidget] Layer ${id} visibility changed to:`, visible);

    onLayerVisibilityChange?.(id, visible); // ✅ 부모 핸들러 호출

  }, [onLayerVisibilityChange]);

  

  const handleOpacityChange = useCallback((id: string, opacity: number) => {

    console.log(`[UseLayerTOCWidget] Layer ${id} opacity changed to:`, opacity);

    onLayerOpacityChange?.(id, opacity); // ✅ 부모 핸들러 호출

  }, [onLayerOpacityChange]);

  

  return (

    <div className={className}>

      {tocNodes.map(node => (

        <div key={node.id} className="flex items-center gap-2 p-2">

          <button

            onClick={() => handleToggleVisibility(node.id, !node.visible)}

            className="p-1"

          >

            {node.visible ? '👁️' : '🙈'}

          </button>

          <span>{node.name}</span>

          <input

            type="range"

            min="0"

            max="1"

            step="0.1"

            value={node.opacity || 1}

            onChange={(e) => handleOpacityChange(node.id, parseFloat(e.target.value))}

          />

        </div>

      ))}

    </div>

  );

}


```

### 🔑 핵심 개선점

1. **기존 Layer 컴포넌트 활용**: 이미 구현된 useEffect들을 그대로 사용

2. **단일 상태 변경 경로**: 모든 변경이 ServiceMap → props → Layer useEffect 경로

3. **React DevTools 호환**: props 변화를 추적 가능

4. **테스트 용이성**: 명시적인 props 기반 제어

### 🎯 패턴 선택 가이드

| 기준 | 패턴 A (useLayer 중심) | 패턴 B (Props 기반) |

|------|---------------------|-------------------|

| **복잡도** | 🟢 낮음 | 🟡 중간 |

| **현재 구조 호환성** | 🟢 높음 | 🔴 낮음 |

| **상태 관리** | 🟢 단일 진실 공급원 | 🟡 두 단계 동기화 |

| **디버깅** | 🟢 쉬움 | 🟡 중간 |

| **확장성** | 🟢 좋음 | 🟢 좋음 |

| **학습 곡선** | 🟢 낮음 | 🟡 중간 |

| **완전한 제어** | 🟡 제한적 | 🟢 완전함 |

#### 권장 사항

**✅ 패턴 A 사용 시나리오 (권장)**

- 현재 구조를 유지하고 싶은 경우

- 간단하고 직관적인 구조를 원하는 경우

- useLayer Hook의 기능을 최대한 활용하고 싶은 경우

- 빠른 개발과 유지보수를 원하는 경우

**✅ 패턴 B 사용 시나리오**

- 상위 컴포넌트에서 모든 상태를 완전히 제어해야 하는 경우

- 복잡한 상태 로직이나 외부 시스템과의 연동이 필요한 경우

- React의 표준 패턴을 선호하는 경우

### 📋 현재 구조 평가

#### ✅ 이미 적절하게 구현된 부분

1. **ServiceLayerManager** (`service-layer-manager.tsx:20`)

   ```typescript

   // ✅ 이미 선언적 방식으로 구현됨

   const layerComponents = useMemo(() => {

     return layerNodes.map((layerNode) => (

       <Layer

         key={layerNode.id}

         id={layerNode.id}

         visible={layerNode.visible ?? true}  // 초기값 제공

         opacity={layerNode.opacity ?? 1}    // 초기값 제공

       />

     ));

   }, [layerNodes]);

   ```

2. **UseLayerTOCWidget** (`toc-widget.tsx`)

   ```typescript

   // ✅ 이미 useLayer만 사용하여 상태 관리

   const handleToggleVisibility = useCallback((id: string, visible: boolean) => {

     setVisible(id, visible); // useLayer Hook 사용

   }, [setVisible]);

   ```

3. **useLayer Hook** (`use-layer.ts:261`)

   ```typescript

   // ✅ 이미 중앙 집중식 상태 관리 구현

   const layers = layerStore((state) => state.layers);

   const setVisible = useCallback((layerId: string, visible: boolean) => {

     // Layer Store를 통한 안전한 상태 관리

   }, [layers, updateLayerInStore]);

   ```

#### 🎉 결론: 현재 구조는 이미 패턴 A (useLayer 중심)로 올바르게 구현되어 있음

현재 codebase는 이미 권장되는 **패턴 A (useLayer 중심)**으로 구현되어 있습니다:

- ✅ **ServiceLayerManager**: 선언적으로 Layer 컴포넌트들을 렌더링

- ✅ **Layer 컴포넌트**: props로 초기값을 받아 useLayer Hook에 전달

- ✅ **TOC Widget**: useLayer Hook을 통해서만 상태 접근/변경

- ✅ **useLayer Hook**: Layer Store를 통한 중앙 집중식 상태 관리

## 구현 세부사항

### 현재 구조 분석

1. **ServiceMap** (`apps/web/app/service/_components/common/service-map.tsx:79`)

   - 최상위 컴포넌트로 MapContainer 포함

   - ServiceLayerManager와 TOCWidgetService를 렌더링

2. **ServiceLayerManager** (`apps/web/app/service/_components/widgets/service-layer-manager.tsx:20`)

   - 서비스별 TOC 데이터를 로드하여 Layer 컴포넌트들을 렌더링

   - 이미 선언적 방식으로 구현됨

3. **TOCWidget** (`apps/web/components/widget/toc-widget.tsx:27`)

   - UseLayerTOCWidget을 래핑

   - useLayer 상태만 사용하여 이중 상태 관리 문제 해결

4. **useLayer Hook** (`packages/geon-map/react/odf/src/hooks/use-layer.ts:261`)

   - 통합 Layer 관리 훅

   - Layer Store와 상호작용하여 상태 관리

### 실제 변경 필요사항 (재평가)

#### 🎉 변경 불필요 - 이미 올바르게 구현됨

현재 구조 분석 결과, **별도의 큰 변경 없이 이미 선언적 컴포넌트 방식으로 구현되어 있습니다**:

1. **✅ TOC Widget** - 이미 적절함

   - UseLayerTOCWidget이 useLayer Hook만 사용하여 상태 관리

   - 직접적인 layer 조작이 아닌 useLayer를 통한 안전한 상태 변경

2. **✅ Layer Component** - 이미 적절함

   - ServiceLayerManager가 선언적으로 Layer 컴포넌트들을 렌더링

   - props로 초기값을 제공하고 useLayer Hook이 실제 상태 관리

3. **✅ 상태 관리** - 이미 적절함

   - useLayer Hook이 Layer Store를 통해 중앙 집중식 상태 관리

   - 단일 진실 공급원 (Single Source of Truth) 구현

#### 🔧 최소한의 개선 사항 (선택적)

필수는 아니지만 고려할 수 있는 개선사항들:

1. **배경지도 위젯과의 연동 검증**

   - 배경지도 스위치 기능이 Layer 컴포넌트와 잘 동작하는지 확인

   - 필요시 baseLayer 관리 방식 통일

2. **분할지도와의 연계 동작 확인**

   - 분할 모드에서 Layer 상태 동기화 확인

   - swipe 기능과 Layer 가시성 연동 테스트

3. **성능 최적화**

   - Layer 컴포넌트의 불필요한 리렌더링 방지

   - useMemo/useCallback 최적화 검토

## 이미 달성된 효과

현재 구조가 이미 선언적 컴포넌트 패턴으로 구현되어 다음 효과들이 **이미 달성되었습니다**:

### ✅ 달성된 효과들

1. **✅ 유지보수성 향상**

   - useLayer Hook을 통한 중앙 집중식 상태 관리 구현

   - ServiceLayerManager의 명확한 데이터 흐름

2. **✅ 디버깅 용이성**

   - useLayer Hook을 통한 단일 상태 변경 경로

   - Layer Store를 통한 예측 가능한 상태 관리

3. **✅ 확장성**

   - ServiceLayerManager의 선언적 Layer 렌더링으로 일관된 패턴

   - Layer 컴포넌트의 높은 재사용성

4. **✅ 성능**

   - useMemo를 활용한 Layer 컴포넌트 최적화 (service-layer-manager.tsx:141)

   - useCallback을 통한 핸들러 메모이제이션

### 🔍 검증 필요 사항

현재 구조의 안정성 확인을 위해:

1. **배경지도 위젯 연동 검증**

   - ToolbarBasemap과 Layer 상태 동기화 확인

2. **분할지도 연계 동작 검증**

   - ToolbarSwipe와 Layer 가시성 연동 테스트

3. **TOC Widget 사용자 경험**

   - TOCWidget의 현재 동작이 예상대로 작동하는지 확인

### 🎯 결론

**"layer 선언적 컴포넌트 사용 방식으로 변경"** 작업은 **이미 완료된 상태**입니다.

현재 codebase는 권장되는 패턴으로 올바르게 구현되어 있으며, 추가 개발보다는 **기존 기능의 검증과 테스트**에 집중하는 것이 적절합니다.

---

> 📝 **참고**: 이 변경사항은 React의 선언적 패러다임을 따르며, 상태 관리를 단순화하여 유지보수성을 크게 향상시킵니다.
