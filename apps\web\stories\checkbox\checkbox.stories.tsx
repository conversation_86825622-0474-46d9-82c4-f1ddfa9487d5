import { Checkbox } from "@geon-ui/react/primitives/checkbox";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import CheckboxDemoSource from "!!raw-loader!./demo";

import { CheckboxDemo } from "./demo";

const meta = {
  title: "Shadcn/Checkbox",
  component: Checkbox,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Checkbox>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: CheckboxDemoSource,
      },
    },
  },
  render: () => <CheckboxDemo />,
};
