import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import SkeletonDemoSource from "!!raw-loader!./demo";

import { SkeletonDemo } from "./demo";

const meta = {
  title: "Shadcn/Skeleton",
  component: Skeleton,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Skeleton>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: SkeletonDemoSource,
      },
    },
  },
  render: () => <SkeletonDemo />,
};
