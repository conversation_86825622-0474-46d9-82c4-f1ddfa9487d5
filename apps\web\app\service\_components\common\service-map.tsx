"use client";

import { Layer, MapContainer } from "@geon-map/react-odf";
import { useBaseLayer, useMap } from "@geon-map/react-odf";
import {
  AddressSearch,
  AddressSearchContent,
  AddressSearchEmpty,
  AddressSearchInput,
  AddressSearchItem,
  AddressSearchList,
  AddressSearchSelect,
  AddressSearchTrigger,
  EupmyeondongSelector,
  LiSelector,
  RegionSelector,
  SidoSelector,
  SigunguSelector,
  SimpleTOC,
  SwipeDragHandle,
  ToolbarBasemap,
  ToolbarBasemapContent,
  ToolbarBasemapTrigger,
  ToolbarClear,
  ToolbarContainer,
  ToolbarDownload,
  ToolbarDownloadContent,
  ToolbarDownloadItem,
  ToolbarDownloadTrigger,
  ToolbarDraw,
  ToolbarDrawAction,
  ToolbarDrawContent,
  ToolbarDrawTool,
  ToolbarDrawTrigger,
  ToolbarElevation,
  ToolbarElevationContent,
  ToolbarElevationTool,
  ToolbarElevationTrigger,
  ToolbarHome,
  ToolbarMeasure,
  ToolbarMeasureAction,
  ToolbarMeasureContent,
  ToolbarMeasureTool,
  ToolbarMeasureTrigger,
  ToolbarPrint,
  ToolbarPrintContent,
  ToolbarPrintTrigger,
  ToolbarRoadview,
  ToolbarRoadviewContent,
  ToolbarRoadviewTrigger,
  ToolbarSplit,
  ToolbarSplitContent,
  ToolbarSplitOption,
  ToolbarSplitTrigger,
  ToolbarSwipe,
  ToolbarSwipeContent,
  ToolbarSwipeTrigger,
  useAddressSearch,
  useRegionSelector,
  useSplitMode,
  ZoomControl,
} from "@geon-map/react-ui/components";
import type { TOCNode } from "@geon-map/react-ui/types";
import { createGeonAddrgeoClient, crtfckey, WMS_URL } from "@geon-query/model";

// 🚀 완전한 선언형을 위한 LayerNode 인터페이스
interface LayerNode {
  id: string;
  name: string;
  type: "layer";
  visible: boolean;
  opacity: number;
  zIndex: number;
  layerId?: string; // geoserver layer 이름
}
import { Button } from "@geon-ui/react/primitives/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import { Separator } from "@geon-ui/react/primitives/separator";
import { Slider } from "@geon-ui/react/primitives/slider";
import { Layers, UnfoldHorizontal } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";

import { useFacilitySearch } from "@/app/service/_contexts/facility-search";

export function ServiceMap() {
  const { splitMode, setSplitMode } = useSplitMode();
  const { availableBaseLayers } = useBaseLayer();
  const { map } = useMap();
  const { selectedServiceId, appliedCQLFilter } = useFacilitySearch();

  const [swipeValue, setSwipeValue] = useState(50);
  const [swipeEnabled, setSwipeEnabled] = useState(false);
  const [mapSize, setMapSize] = useState<[number, number] | null>(null);
  const [tocVisible, setTOCVisible] = useState(false);

  const [leftLayerId, setLeftLayerId] = useState("eMapBasic");
  const [rightLayerId, setRightLayerId] = useState("BM0000000012");

  // 🚀 완전한 선언형: layers가 단일 진실 공급원
  const [layers, setLayers] = useState<LayerNode[]>([]);

  // 🚀 TOC 데이터 로드 및 layers 상태 변환
  useEffect(() => {
    const loadLayers = async () => {
      if (!selectedServiceId) {
        setLayers([]);
        return;
      }

      try {
        // 공통 데이터는 항상 로드
        const commonModule = await import(`@/app/service/_data/toc/common`);
        const commonData = commonModule.TOC_DATA;

        let finalTocData: TOCNode[] = [];
        try {
          // 서비스별 데이터 로드 시도
          const serviceModule = await import(
            `@/app/service/_data/toc/${selectedServiceId}`
          );
          const serviceData = serviceModule.TOC_DATA;
          finalTocData = [...serviceData, ...commonData];
        } catch (serviceError) {
          // 서비스 데이터 로드 실패 시 공통 데이터만 반환
          finalTocData = commonData;
        }

        // 🎯 TOC 데이터에서 레이어만 추출하여 LayerNode로 변환
        const extractLayers = (nodes: TOCNode[]): TOCNode[] => {
          const layers: TOCNode[] = [];
          const traverse = (nodes: TOCNode[]) => {
            nodes.forEach((node) => {
              if (node.type === "layer") {
                layers.push(node);
              } else if (node.children) {
                traverse(node.children);
              }
            });
          };
          traverse(nodes);
          return layers;
        };

        const extractedLayers = extractLayers(finalTocData);
        const layerNodes: LayerNode[] = extractedLayers.map((node) => ({
          id: node.id,
          name: node.name,
          type: "layer" as const,
          visible: node.visible ?? true,
          opacity: node.opacity ?? 1,
          zIndex: node.zIndex ?? 0,
          layerId: node.id, // geoserver layer 이름
        }));

        console.log("[ServiceMap] 🚀 Loaded layers:", layerNodes);
        setLayers(layerNodes);
      } catch (error) {
        console.error("[ServiceMap] Failed to load layers:", error);
        setLayers([]);
      }
    };

    loadLayers();
  }, [selectedServiceId]);

  // 🚀 순수 함수 기반 상태 업데이트
  const updateLayerVisibility = useCallback(
    (layers: LayerNode[], layerId: string, visible: boolean): LayerNode[] => {
      return layers.map((layer) =>
        layer.id === layerId ? { ...layer, visible } : layer,
      );
    },
    [],
  );

  const updateLayerOpacity = useCallback(
    (layers: LayerNode[], layerId: string, opacity: number): LayerNode[] => {
      return layers.map((layer) =>
        layer.id === layerId ? { ...layer, opacity } : layer,
      );
    },
    [],
  );

  const updateLayerZIndex = useCallback(
    (layers: LayerNode[], layerId: string, zIndex: number): LayerNode[] => {
      return layers.map((layer) =>
        layer.id === layerId ? { ...layer, zIndex } : layer,
      );
    },
    [],
  );

  // 🚀 완전한 선언형 핸들러들
  const handleLayerVisibilityChange = useCallback(
    (layerId: string, visible: boolean) => {
      console.log("[ServiceMap] 🎯 Layer visibility change:", {
        layerId,
        visible,
      });
      setLayers((prev) => updateLayerVisibility(prev, layerId, visible));
    },
    [updateLayerVisibility],
  );

  const handleLayerOpacityChange = useCallback(
    (layerId: string, opacity: number) => {
      console.log("[ServiceMap] 🎯 Layer opacity change:", {
        layerId,
        opacity,
      });
      setLayers((prev) => updateLayerOpacity(prev, layerId, opacity));
    },
    [updateLayerOpacity],
  );

  const handleLayerZIndexChange = useCallback(
    (layerId: string, zIndex: number) => {
      console.log("[ServiceMap] 🎯 Layer zIndex change:", { layerId, zIndex });
      setLayers((prev) => updateLayerZIndex(prev, layerId, zIndex));
    },
    [updateLayerZIndex],
  );

  // 그룹 토글 핸들러 (현재는 그룹이 없으므로 빈 함수)
  const handleGroupToggle = useCallback(
    (groupId: string, expanded: boolean) => {
      console.log("[ServiceMap] 🎯 Group toggle (no-op):", {
        groupId,
        expanded,
      });
      // 현재 SimpleTOC에는 그룹이 없으므로 아무것도 하지 않음
    },
    [],
  );

  // 🚀 Layer 컴포넌트들을 직접 렌더링하기 위한 메모이제이션
  const baseConfig = useMemo(
    () => ({
      type: "geoserver" as const,
      server: { url: WMS_URL },
      crtfckey,
      service: "wms" as const,
      method: "post" as const,
      fit: false,
    }),
    [],
  );

  const layerComponents = useMemo(() => {
    return layers.map((layerNode) => {
      return (
        <Layer
          key={layerNode.id}
          id={layerNode.id}
          config={{
            ...baseConfig,
            layer: layerNode.layerId || layerNode.id,
            name: layerNode.name,
          }}
          visible={layerNode.visible}
          opacity={layerNode.opacity}
          zIndex={layerNode.zIndex}
          cqlFilter={appliedCQLFilter}
          onLayerReady={(mapLayerId: string) => {
            console.log(
              `[ServiceMap] ✅ Layer ready: ${layerNode.id} -> ${mapLayerId}`,
            );
          }}
          onLayerError={(error: Error) => {
            console.error(
              `[ServiceMap] ❌ Layer error: ${layerNode.id}`,
              error,
            );
          }}
        />
      );
    });
  }, [layers, baseConfig, appliedCQLFilter]);

  const apiClient = createGeonAddrgeoClient({
    baseUrl: "https://city.geon.kr/api/",
    crtfckey: "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0",
  });

  const { handleAddressSearch, isLoading } = useAddressSearch({
    apiType: "geon",
    apiClient,
  });
  const { handleRegionSelect, handleRegionList, handlePnuSelect } =
    useRegionSelector({
      apiType: "geon",
      apiClient,
    });

  // 맵 크기 추적
  useEffect(() => {
    if (!map) return;

    const updateMapSize = () => {
      const size = map.getSize();
      if (size) {
        setMapSize(size as [number, number]);
      }
    };

    updateMapSize();
    map.on("change:size", updateMapSize);

    return () => {
      map.un("change:size", updateMapSize);
    };
  }, [map]);

  return (
    <MapContainer className="relative h-full w-full" splitMode={splitMode}>
      {/* 🚀 완전한 선언형: layers 상태를 직접 Layer 컴포넌트로 렌더링 */}
      {layerComponents}

      {/* ✅ SimpleTOC 직접 사용 */}
      <div className="absolute left-0 top-0 z-50 m-5">
        <Button
          size="sm"
          onClick={() => setTOCVisible(!tocVisible)}
          className="h-8 w-8 bg-white p-0 text-green-600 transition-transform hover:scale-110 hover:bg-white hover:text-green-600"
        >
          <Layers className="h-4 w-4" />
        </Button>

        {tocVisible && (
          <SimpleTOC
            layers={layers}
            onLayerToggle={handleLayerVisibilityChange}
            onLayerOpacityChange={handleLayerOpacityChange}
            onGroupToggle={handleGroupToggle}
            className="absolute left-0 top-12 max-h-[700px] min-h-[300px] w-[400px]"
            showHeader={true}
            isLayerOpacityEnabled={true}
          />
        )}
      </div>

      <RegionSelector
        className="absolute left-1/3 top-4 flex items-center gap-2"
        fetchRegionInfo={handleRegionSelect}
        fetchRegionList={handleRegionList}
        fetchPnu={handlePnuSelect}
        useLi={false}
      >
        <SidoSelector />
        <SigunguSelector />
        <EupmyeondongSelector />
        <LiSelector />
      </RegionSelector>

      <AddressSearch
        className="right-4"
        onSearch={handleAddressSearch}
        isLoading={isLoading}
      >
        <div className="flex gap-2">
          <AddressSearchSelect />
          <AddressSearchInput placeholder="검색어를 입력해주세요." />
          <AddressSearchTrigger></AddressSearchTrigger>
        </div>
        <AddressSearchContent>
          <AddressSearchList isLoading={isLoading}>
            <AddressSearchEmpty />
            <AddressSearchItem />
          </AddressSearchList>
        </AddressSearchContent>
      </AddressSearch>

      {/* 메인 툴바 - 포지션에 따라 결정 */}
      <ToolbarContainer
        // posistion 에 따라 popover, tooltip 방향 결정 확인용
        position="center-right"
        // style variant 는 좀 더 확인..
        buttonVariant="separated"
        variant="glass"
      >
        <ToolbarHome tooltip="홈 위치" />

        <ToolbarBasemap>
          <ToolbarBasemapTrigger tooltip="배경지도" />
          <ToolbarBasemapContent />
        </ToolbarBasemap>

        <Separator orientation="vertical" />

        <ToolbarDraw>
          <ToolbarDrawTrigger tooltip="그리기 도구" />
          <ToolbarDrawContent>
            <ToolbarDrawTool mode="point" />
            <ToolbarDrawTool mode="lineString" />
            <ToolbarDrawTool mode="polygon" />
            <ToolbarDrawTool mode="box" />
            <ToolbarDrawTool mode="circle" />
            <ToolbarDrawAction action="stop" />
          </ToolbarDrawContent>
        </ToolbarDraw>

        <ToolbarMeasure>
          <ToolbarMeasureTrigger tooltip="측정 도구" />
          <ToolbarMeasureContent>
            <ToolbarMeasureTool mode="measure-distance" />
            <ToolbarMeasureTool mode="measure-area" />
            <ToolbarMeasureTool mode="measure-round" />
            <ToolbarMeasureTool mode="measure-spot" />
            <ToolbarMeasureAction action="stop" />
          </ToolbarMeasureContent>
        </ToolbarMeasure>

        <ToolbarElevation>
          <ToolbarElevationTrigger tooltip="경사도 측정" />
          <ToolbarElevationContent>
            <ToolbarElevationTool elevationType="lineString" />
            <ToolbarElevationTool elevationType="point" />
          </ToolbarElevationContent>
        </ToolbarElevation>

        <ToolbarClear tooltip="모든 그리기 삭제" />

        <Separator orientation="vertical" />

        <ToolbarSplit value={splitMode} onValueChange={setSplitMode}>
          <ToolbarSplitTrigger tooltip="분할지도" />
          <ToolbarSplitContent>
            <ToolbarSplitOption count={1} />
            <ToolbarSplitOption count={2} />
            <ToolbarSplitOption count={3} />
            <ToolbarSplitOption count={4} />
          </ToolbarSplitContent>
        </ToolbarSplit>

        {/* 스와이프. 기본 UI를 어떻게 제공할지에 대한 고민*/}
        <ToolbarSwipe
          value={swipeValue}
          onValueChange={setSwipeValue}
          enabled={swipeEnabled}
          onEnabledChange={setSwipeEnabled}
          leftLayerId={leftLayerId}
          rightLayerId={rightLayerId}
          onLeftLayerChange={setLeftLayerId}
          onRightLayerChange={setRightLayerId}
        >
          <ToolbarSwipeTrigger tooltip="스와이프" />
          <ToolbarSwipeContent>
            {/* 활성화/비활성화 토글 버튼 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSwipeEnabled(!swipeEnabled)}
              className="w-full justify-start"
            >
              <UnfoldHorizontal className="mr-2 h-4 w-4" />
              {swipeEnabled ? "스와이프 비활성화" : "스와이프 활성화"}
            </Button>

            {/* 스와이프 위치 슬라이더 */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">스와이프 위치</span>
                <span className="text-muted-foreground text-xs">
                  {swipeValue}%
                </span>
              </div>
              <Slider
                value={[swipeValue]}
                onValueChange={(values) => setSwipeValue(values[0] ?? 50)}
                min={0}
                max={100}
                step={1}
                className="w-full"
              />
            </div>

            {/* 레이어 선택 */}
            <div className="grid grid-cols-2 gap-3">
              {/* 왼쪽 레이어 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">왼쪽 레이어</label>
                <Select value={leftLayerId} onValueChange={setLeftLayerId}>
                  <SelectTrigger>
                    <SelectValue placeholder="레이어 선택" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableBaseLayers?.map((layer) => (
                      <SelectItem key={layer.id} value={layer.id}>
                        {layer.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 오른쪽 레이어 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">오른쪽 레이어</label>
                <Select value={rightLayerId} onValueChange={setRightLayerId}>
                  <SelectTrigger>
                    <SelectValue placeholder="레이어 선택" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableBaseLayers?.map((layer) => (
                      <SelectItem key={layer.id} value={layer.id}>
                        {layer.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </ToolbarSwipeContent>
        </ToolbarSwipe>

        <Separator orientation="vertical" />

        <ToolbarDownload>
          <ToolbarDownloadTrigger tooltip="다운로드" />
          <ToolbarDownloadContent>
            <ToolbarDownloadItem downloadType="png" />
            <ToolbarDownloadItem downloadType="pdf" />
          </ToolbarDownloadContent>
        </ToolbarDownload>

        <ToolbarPrint>
          <ToolbarPrintTrigger tooltip="인쇄" />
          <ToolbarPrintContent />
        </ToolbarPrint>

        <Separator orientation="vertical" />

        <ToolbarRoadview>
          <ToolbarRoadviewTrigger tooltip="로드뷰" />
          <ToolbarRoadviewContent />
        </ToolbarRoadview>
      </ToolbarContainer>

      {/* 스와이프 드래그 핸들 */}
      <SwipeDragHandle
        value={swipeValue}
        onValueChange={setSwipeValue}
        mapSize={mapSize}
        visible={swipeEnabled}
        className="z-40"
      />

      {/* 줌 컨트롤 - 왼쪽 하단 */}
      <ZoomControl />
    </MapContainer>
  );
}
