"use client";

import { Coordinate } from "@geon-map/core";
import { useMap } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Label } from "@geon-ui/react/primitives/label";
import { Popover, PopoverTrigger } from "@geon-ui/react/primitives/popover";
import { Switch } from "@geon-ui/react/primitives/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@geon-ui/react/primitives/tooltip";
import { Webcam } from "lucide-react";
import { Resizable } from "re-resizable";
import * as React from "react";
import { useEffect, useRef, useState } from "react";

import { useRoadview } from "../../hooks/use-roadview";
import {
  ToolbarContent,
  ToolbarItem,
  ToolbarTrigger,
} from "./base/toolbar-item";

const API_KEY = "40f4f6b64e7ede9838110b5197156f44";

// Context for ToolbarRoadview
interface RoadviewContextValue {
  enabled: boolean;
  size: { width: number; height: number };
  center: Coordinate;
  marker: any;
  setSize: (size: { width: number; height: number }) => void;
  setCenter: (center: Coordinate) => void;
  setMarker: (marker: any) => void;
  handleToggle: (checked: boolean, marker: any) => void;
}

const RoadviewContext = React.createContext<RoadviewContextValue | null>(null);

const useRoadviewContext = () => {
  const context = React.useContext(RoadviewContext);
  if (!context) {
    throw new Error(
      "ToolbarRoadview components must be used within ToolbarRoadview",
    );
  }
  return context;
};

// Props for compound components
export interface ToolbarRoadviewProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export interface ToolbarRoadviewTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
}

export interface ToolbarRoadviewContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

// RoadviewContent Component
interface RoadviewContentProps {
  width: number;
  height: number;
  center: [number, number];
  marker: any;
}

declare global {
  interface Window {
    kakao: any;
    KakaoRoadView: any;
  }
}

export const RoadviewContent = ({
  width,
  height,
  center,
  marker,
}: RoadviewContentProps) => {
  const rvWrapperRef = useRef<HTMLDivElement>(null);
  const rvRef = useRef<any>(null);
  const rvClientRef = useRef<any>(null);
  const { map, odf } = useMap();

  useEffect(() => {
    if (map && odf && rvWrapperRef.current) {
      loadKakaoScript().then(() => {
        initRoadview();
      });
    }
  }, [map, odf]);

  useEffect(() => {
    loadKakaoScript().then(() => {
      moveRoadview();
    });
  }, [center]);

  const loadKakaoScript = () => {
    return new Promise<void>((resolve) => {
      if (window.kakao?.maps) {
        window.kakao.maps.load(resolve);
        return;
      }

      const script = document.createElement("script");
      script.src = `//dapi.kakao.com/v2/maps/sdk.js?appkey=${API_KEY}&autoload=false`;
      script.onload = () => window.kakao.maps.load(resolve);
      document.head.appendChild(script);
    });
  };

  const to_map_projection = (x: number, y: number) => {
    const projection4326 = new odf.Projection({ EPSG: "4326" });
    return projection4326.unproject(
      [x, y],
      map.getView().getProjection().getCode().split(":")[1],
    );
  };

  const to_4326 = () => {
    const projection = map?.getProjection();
    const center4326 = projection.unproject(center, "4326");
    return new window.kakao.maps.LatLng(center4326[1], center4326[0]);
  };

  const moveRoadview = () => {
    const position = to_4326();
    if (!position) return;

    rvClientRef?.current?.getNearestPanoId(position, 50, (panoId: number) => {
      if (!panoId) {
        console.warn("도로가 존재하지 않는 지역입니다.");
      } else {
        rvRef.current.setPanoId(panoId, position);
        rvRef.current.relayout();
      }
    });
  };

  const roadviewEvent = () => {
    window.kakao.maps.event.addListener(
      rvRef.current,
      "position_changed",
      () => {
        if (odf && marker) {
          const rvPosition = rvRef.current.getPosition();
          const xyMapProjection = to_map_projection(
            rvPosition.La,
            rvPosition.Ma,
          );
          marker.setPosition(new odf.Coordinate(xyMapProjection));
          map?.setCenter(new odf.Coordinate(xyMapProjection));
        }
      },
    );
  };

  const initRoadview = () => {
    if (!rvWrapperRef.current) return;
    rvRef.current = new window.kakao.maps.Roadview(rvWrapperRef.current);
    rvClientRef.current = new window.kakao.maps.RoadviewClient();
    roadviewEvent();
    moveRoadview();
  };

  return (
    <div
      ref={rvWrapperRef}
      style={{ width: width - 20, height: height - 60 }}
      className="mt-2 border rounded w-[400px] h-[300px] overflow-hidden"
    />
  );
};

// RoadviewWidget Component (Resizable container)
export const RoadviewWidget = () => {
  const context = React.useContext(RoadviewContext);

  // Context가 없으면 아무것도 렌더링하지 않음
  if (!context) return null;

  const { enabled, size, center, marker, setSize } = context;

  if (!enabled || center[0] === 0) return null;

  return (
    <Resizable
      size={{ width: size.width, height: "auto" }}
      style={{ position: "absolute" }}
      minWidth={160}
      maxWidth={1000}
      maxHeight={640}
      enable={{
        top: true,
        right: true,
        bottom: true,
        left: true,
        topRight: true,
        bottomRight: true,
        bottomLeft: true,
        topLeft: true,
      }}
      onResizeStop={(_, __, ref) => {
        const width = ref.offsetWidth;
        const height = ref.offsetHeight;
        setSize({ width, height });
      }}
      className="absolute right-30 bottom-0 z-[50] rounded-lg border p-4 shadow-sm bg-white text-sm space-y-3"
    >
      <div className="flex items-center gap-3">
        <Label className="text-sm font-medium">로드뷰 표시</Label>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="text-[13px] text-gray-700 cursor-help truncate max-w-[150px]">
                로드뷰를 조작 후 방향키, 스페이스 바를 이용하여 조작이
                가능합니다.
              </div>
            </TooltipTrigger>
            <TooltipContent
              side="top"
              sideOffset={9}
              className="text-[13px] max-w-[250px]"
            >
              로드뷰를 조작 후 방향키, 스페이스 바를 이용하여 조작이 가능합니다.
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <RoadviewContent
        width={size.width}
        height={size.height}
        center={center}
        marker={marker}
      />
    </Resizable>
  );
};

// Main ToolbarRoadview Container (Context Provider)
export const ToolbarRoadview = React.forwardRef<
  HTMLDivElement,
  ToolbarRoadviewProps
>(({ className, children, ...props }, ref) => {
  const [size, setSize] = useState({ width: 420, height: 300 });
  const [center, setCenter] = useState<Coordinate>([0, 0]);
  const [marker, setMarker] = useState<any>(null);

  const { enabled, handleToggle } = useRoadview({
    onMarkerSelect: setMarker,
    onCenterSelect: setCenter,
  });

  const contextValue = React.useMemo(
    () => ({
      enabled,
      size,
      center,
      marker,
      setSize,
      setCenter,
      setMarker,
      handleToggle,
    }),
    [
      enabled,
      size,
      center,
      marker,
      setSize,
      setCenter,
      setMarker,
      handleToggle,
    ],
  );

  return (
    <RoadviewContext.Provider value={contextValue}>
      <ToolbarItem ref={ref} className={className} {...props}>
        <Popover>
          <PopoverTrigger asChild>
            {React.Children.toArray(children).find(
              (child) =>
                React.isValidElement(child) &&
                child.type === ToolbarRoadviewTrigger,
            )}
          </PopoverTrigger>
          {React.Children.toArray(children).find(
            (child) =>
              React.isValidElement(child) &&
              child.type === ToolbarRoadviewContent,
          )}
        </Popover>
        {/* RoadviewWidget */}
        <RoadviewWidget />
      </ToolbarItem>
    </RoadviewContext.Provider>
  );
});

ToolbarRoadview.displayName = "ToolbarRoadview";

// ToolbarRoadviewTrigger Component
export const ToolbarRoadviewTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarRoadviewTriggerProps
>(
  (
    { tooltip = "로드뷰", size = "default", className, children, ...props },
    ref,
  ) => {
    const context = React.useContext(RoadviewContext);

    // Context가 없으면 기본값 사용
    const enabled = context?.enabled ?? false;

    return (
      <ToolbarTrigger
        ref={ref}
        tooltip={tooltip}
        size={size}
        active={enabled}
        className={className}
        {...props}
      >
        {children || <Webcam className="h-4 w-4" />}
      </ToolbarTrigger>
    );
  },
);

ToolbarRoadviewTrigger.displayName = "ToolbarRoadviewTrigger";

// ToolbarRoadviewContent Component
export const ToolbarRoadviewContent = React.forwardRef<
  HTMLDivElement,
  ToolbarRoadviewContentProps
>(({ className, children, ...props }, ref) => {
  const context = React.useContext(RoadviewContext);

  // Context가 없으면 기본값 사용
  const enabled = context?.enabled ?? false;
  const marker = context?.marker ?? null;
  const handleToggle = context?.handleToggle ?? (() => {});

  return (
    <ToolbarContent
      ref={ref}
      align="center"
      sideOffset={16}
      className={cn("w-fit flex flex-col gap-3 p-4", className)}
      {...props}
    >
      <div className="flex items-center gap-3">
        <Label htmlFor="roadview-switch" className="text-sm font-medium">
          로드뷰 표시
        </Label>
        <Switch
          id="roadview-switch"
          checked={enabled}
          onCheckedChange={(checked) => handleToggle(checked, marker)}
        />
      </div>
      {enabled && (
        <div className="text-[13px] text-gray-700 max-w-[200px]">
          지도에 지점을 선택하여 로드뷰를 확인하세요.
        </div>
      )}
      {children}
    </ToolbarContent>
  );
});

ToolbarRoadviewContent.displayName = "ToolbarRoadviewContent";
