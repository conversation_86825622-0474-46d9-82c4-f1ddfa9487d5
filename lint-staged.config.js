/** @type {import("lint-staged").Configuration} */
export default {
  "**/*.+(ts|tsx)": [
    // () => "pnpm type-check",
  ],
  "apps/web/**/*.+(ts|tsx)": [
    // lint fix
    (filenames) => `pnpm --filter web eslint --fix --cache ${filenames.join(' ')}`,
  ],
  "packages/geon-ui/react/**/*.+(ts|tsx)": [
    // lint fix
    (filenames) => `pnpm --filter @geon-ui/react eslint --fix --cache ${filenames.join(' ')}`,
  ],
  "packages/geon-map/react-odf/**/*.+(ts|tsx)": [  
    // lint fix
    (filenames) => `pnpm --filter @geon-map/react-odf eslint --fix --cache ${filenames.join(' ')}`,
  ],
  "packages/geon-map/react-ui/**/*.+(ts|tsx)": [  
    // lint fix
    (filenames) => `pnpm --filter @geon-map/react-ui eslint --fix --cache ${filenames.join(' ')}`,
  ],
  "packages/geon-query/model/**/*.+(ts|tsx)": [
    // lint fix
    (filenames) => `pnpm --filter @geon-query/model eslint --fix --cache ${filenames.join(' ')}`,
  ],
  "packages/geon-query/reactQuery/**/*.+(ts|tsx)": [
    // lint fix
    (filenames) => `pnpm --filter @geon-query/react-query eslint --fix --cache ${filenames.join(' ')}`,
  ],
}