import { Label } from "@geon-ui/react/primitives/label";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import LabelDemoSource from "!!raw-loader!./demo";

import { LabelDemo } from "./demo";

const meta = {
  title: "Shadcn/Label",
  component: Label,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Label>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: LabelDemoSource,
      },
    },
  },
  render: () => <LabelDemo />,
};
